//
//  ScreenManagementTest.swift
//  YINDONG
//
//  Created by jj on 2025/6/27.
//

import UIKit

/// 飘屏管理系统测试类
class ScreenManagementTest {
    
    /// 测试基本功能
    static func testBasicFunctionality() {
        // 设置容器视图
        guard let keyWindow = UIApplication.shared.windows.first else {
            print("❌ 无法获取主窗口")
            return
        }
        
        ScreenManagement.shared.setContainer(keyWindow)
        print("✅ 容器视图设置成功")
        
        // 测试顶部飘屏
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemRed,
            position: .top,
            animationType: .slideFromRight,
            stayDuration: 2.0
        )
        print("✅ 顶部飘屏添加成功")
        
        // 延迟添加中间飘屏
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            ScreenManagement.shared.addColorBlockFloating(
                color: .systemBlue,
                position: .center,
                animationType: .fadeInOut,
                stayDuration: 2.0
            )
            print("✅ 中间飘屏添加成功")
        }
        
        // 延迟添加底部飘屏
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            ScreenManagement.shared.addColorBlockFloating(
                color: .systemGreen,
                position: .bottom,
                animationType: .slideFromLeft,
                stayDuration: 2.0
            )
            print("✅ 底部飘屏添加成功")
        }
    }
    
    /// 测试自定义位置
    static func testCustomPosition() {
        guard let keyWindow = UIApplication.shared.windows.first else { return }
        ScreenManagement.shared.setContainer(keyWindow)
        
        // 自定义位置飘屏
        ScreenManagement.shared.addColorBlockFloating(
            color: .systemPurple,
            position: .custom(x: 50, y: 150, width: 250, height: 60),
            animationType: .scaleInOut,
            stayDuration: 3.0
        )
        print("✅ 自定义位置飘屏测试")
    }
    
    /// 测试自定义视图
    static func testCustomView() {
        guard let keyWindow = UIApplication.shared.windows.first else { return }
        ScreenManagement.shared.setContainer(keyWindow)
        
        ScreenManagement.shared.addCustomViewFloating(
            viewCreator: {
                let containerView = UIView()
                containerView.backgroundColor = .systemOrange
                containerView.layer.cornerRadius = 12
                containerView.layer.masksToBounds = true
                
                let label = UILabel()
                label.text = "🎉 测试成功 🎉"
                label.textColor = .white
                label.textAlignment = .center
                label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
                
                containerView.addSubview(label)
                label.translatesAutoresizingMaskIntoConstraints = false
                NSLayoutConstraint.activate([
                    label.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
                    label.centerYAnchor.constraint(equalTo: containerView.centerYAnchor)
                ])
                
                return containerView
            },
            position: .center,
            animationType: .fadeInOut,
            stayDuration: 4.0
        )
        print("✅ 自定义视图飘屏测试")
    }
    
    /// 测试队列管理
    static func testQueueManagement() {
        guard let keyWindow = UIApplication.shared.windows.first else { return }
        ScreenManagement.shared.setContainer(keyWindow)
        
        let colors: [UIColor] = [.systemRed, .systemBlue, .systemGreen, .systemOrange, .systemPurple]
        
        for (index, color) in colors.enumerated() {
            ScreenManagement.shared.addColorBlockFloating(
                color: color,
                position: .center,
                animationType: .fadeInOut,
                stayDuration: 1.5
            )
        }
        print("✅ 队列管理测试 - 添加了\(colors.count)个飘屏")
        
        // 5秒后清空队列
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            ScreenManagement.shared.clearQueue()
            print("✅ 队列已清空")
        }
    }
    
    /// 测试所有动画类型
    static func testAllAnimations() {
        guard let keyWindow = UIApplication.shared.windows.first else { return }
        ScreenManagement.shared.setContainer(keyWindow)
        
        let animations: [(ScreenFloatingAnimationType, String, UIColor)] = [
            (.slideFromRight, "右滑入", .systemRed),
            (.slideFromLeft, "左滑入", .systemBlue),
            (.slideFromTop, "上滑入", .systemGreen),
            (.slideFromBottom, "下滑入", .systemOrange),
            (.fadeInOut, "渐变", .systemPurple),
            (.scaleInOut, "缩放", .systemPink),
            (.none, "无动画", .systemTeal)
        ]
        
        for (index, (animation, name, color)) in animations.enumerated() {
            DispatchQueue.main.asyncAfter(deadline: .now() + TimeInterval(index) * 0.5) {
                ScreenManagement.shared.addCustomViewFloating(
                    viewCreator: {
                        let label = UILabel()
                        label.text = name
                        label.textColor = .white
                        label.backgroundColor = color
                        label.textAlignment = .center
                        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
                        label.layer.cornerRadius = 8
                        label.layer.masksToBounds = true
                        return label
                    },
                    position: .center,
                    animationType: animation,
                    stayDuration: 2.0
                )
                print("✅ 测试动画: \(name)")
            }
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始飘屏管理系统测试")
        
        testBasicFunctionality()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 8.0) {
            testCustomPosition()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 12.0) {
            testCustomView()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 18.0) {
            testQueueManagement()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 25.0) {
            testAllAnimations()
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 35.0) {
            print("🎉 所有测试完成！")
        }
    }
}

// MARK: - 使用方法
/*
 在需要测试的地方调用：
 
 // 运行所有测试
 ScreenManagementTest.runAllTests()
 
 // 或者单独测试某个功能
 ScreenManagementTest.testBasicFunctionality()
 ScreenManagementTest.testCustomPosition()
 ScreenManagementTest.testCustomView()
 ScreenManagementTest.testQueueManagement()
 ScreenManagementTest.testAllAnimations()
 */
