//
//  SkillBannerCell.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import FSPagerView
import SnapKit

class SkillBannerCell: FSPagerViewCell {

    // MARK: - Properties
    var model: SkillLevelModel? {
        didSet {
            guard let model = model else { return }
            updateUI(with: model)
        }
    }

    // MARK: - UI Components
    /// 背景图片
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 12
        return imageView
    }()

    /// 技能图标
    private lazy var skillIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 25
        imageView.clipsToBounds = true
        return imageView
    }()

    /// 技能名称和等级
    private lazy var skillTitleLabel: UILabel = {
        let label = UILabel()
        label.font = .bold(18)
        label.textColor = .white
        label.textAlignment = .left
        return label
    }()

    /// 星级显示容器
    private lazy var starContainer: SkillLevelRatingView = {
        let stackView = SkillLevelRatingView()
        return stackView
    }()

    /// 技能描述
    private lazy var skillDescriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(14)
        label.textColor = UIColor.white.withAlphaComponent(0.9)
        label.numberOfLines = 0
        return label
    }()

    /// 分割线
    private lazy var separatorLine: UIView = {
        let line = UIView()
        line.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        return line
    }()

    /// 符石列表
    private lazy var runeTableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(RuneInfoCell.self, forCellReuseIdentifier: "RuneInfoCell")
        tableView.isScrollEnabled = true
        return tableView
    }()

    // 符石数据
    private var runeList: [RuneListModel] = []

    /// 占位图标签（未开通符文/暂无符石）
    private lazy var placeholderLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    // MARK: - Setup
    private func setupUI() {
        contentView.addSubview(backgroundImageView)
        contentView.addSubview(skillIconView)
        contentView.addSubview(skillTitleLabel)
        contentView.addSubview(starContainer)
        contentView.addSubview(skillDescriptionLabel)
        contentView.addSubview(separatorLine)
        contentView.addSubview(runeTableView)
        contentView.addSubview(placeholderLabel)

        setupConstraints()
    }

    private func setupConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        skillIconView.snp.makeConstraints { make in
            make.left.equalTo(30)
            make.top.equalTo(35)
            make.width.height.equalTo(50)
        }

        skillTitleLabel.snp.makeConstraints { make in
            make.right.equalTo(-32)
            make.top.equalTo(skillIconView)
        }

        starContainer.snp.makeConstraints { make in
            make.right.equalTo(skillTitleLabel)
            make.top.equalTo(skillTitleLabel.snp.bottom).offset(4)
            make.height.equalTo(22)
            make.width.equalTo(120)
        }
        
        separatorLine.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(30)
            make.top.equalTo(skillIconView.snp.bottom).offset(20)
            make.height.equalTo(1)
        }


        skillDescriptionLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(30)
            make.top.equalTo(separatorLine.snp.bottom).offset(16)
        }

     
        runeTableView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(36)
            make.top.equalTo(skillDescriptionLabel.snp.bottom).offset(16)
            make.bottom.equalTo(-25)
        }

        placeholderLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(skillDescriptionLabel.snp.bottom).offset(40)
            make.height.equalTo(40)
        }
    }

    // MARK: - Data Update
    private func updateUI(with model: SkillLevelModel) {
        // 设置背景图片
        let bgImage = UIImage(named: "skill_bg\(model.skillSign)")?.createResizableImageCentered()
        backgroundImageView.image = bgImage

        // 设置技能图标
        skillIconView.setImage(from: model.skillUrl)

        // 设置技能名称和等级
        let skillName = model.skillType.title
        
        var levelText = "Lv\(model.skillLevel)"
        if model.skillAddLevel > 0 {
            levelText = "(Lv\(model.skillLevel)+\(model.skillAddLevel))"
        }
        skillTitleLabel.text = "\(skillName)\(levelText)"

        let allLevel = model.skillLevel + model.skillAddLevel
        // 设置星级
        starContainer.configureAndPlay(withLevel: allLevel)

        // 设置技能描述
        skillDescriptionLabel.text = model.skillDescription ?? ""

        // 设置符石列表
        setupRuneList(runeList: model.runeList, skillLevel: model.skillLevel)
    }


    private func setupRuneList(runeList: [RuneListModel], skillLevel: Int) {
        // 更新数据源
        self.runeList = runeList

        // 判断显示状态
        if skillLevel < 50 {
            // 未达到50级，显示占位文字
            placeholderLabel.text = "暂未开通符文"
            placeholderLabel.isHidden = false
            runeTableView.isHidden = true
        } else if runeList.isEmpty {
            // 达到50级但没有符石
            placeholderLabel.text = "暂无符石"
            placeholderLabel.isHidden = false
            runeTableView.isHidden = true
        } else {
            // 有符石数据，显示符石列表
            placeholderLabel.isHidden = true
            runeTableView.isHidden = false
            runeTableView.reloadData()
        }
    }

}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension SkillBannerCell: UITableViewDataSource, UITableViewDelegate {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return runeList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "RuneInfoCell", for: indexPath) as! RuneInfoCell
        let rune = runeList[indexPath.row]
        cell.configure(with: rune)
        cell.backgroundColor = .clear
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
}
