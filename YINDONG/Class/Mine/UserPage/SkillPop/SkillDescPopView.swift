//
//  SkillDescPopView.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import FSPagerView

class SkillDescPopView: CustomPopupView {
    
    var index: Int = 0
    
    var datas: [SkillLevelModel] = [] {
        didSet {
            banner.reloadData()
            
            if index > 0 {
                Async.main(after: 0.05) {
                    self.banner.scrollToItem(at: self.index, animated: false)
                }
                
            }
        }
    }
    
    lazy var banner: FSPagerView = {
        let banner = FSPagerView()
        banner.backgroundColor = .clear
        banner.delegate = self
        banner.dataSource = self
        banner.layerCornerRadius = 10
        banner.automaticSlidingInterval = 0
        banner.transformer = FSPagerViewTransformer(type: .linear)
        banner.register(SkillBannerCell.self, forCellWithReuseIdentifier: "cell")
        banner.itemSize = CGSizeMake(313, 432)
        return banner
    }()
    
    override func configureUI() {
        super.configureUI()
        backgroundColor = .clear
        
        self.frame = CGRectMake(0, 0, AppTool.screenWidth, 444)
        
        addSubview(banner)
        banner.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(440)
        }
    }
    
}



extension SkillDescPopView: FSPagerViewDelegate, FSPagerViewDataSource {
    
    func numberOfItems(in pagerView: FSPagerView) -> Int {
        return self.datas.count
    }
    
    func pagerView(_ pagerView: FSPagerView, cellForItemAt index: Int) -> FSPagerViewCell {
        let cell = pagerView.dequeueReusableCell(withReuseIdentifier: "cell", at: index)
        if let cell = cell as? SkillBannerCell {
            cell.model = self.datas[index]
        }

        return cell
    }
    
    func pagerViewDidScroll(_ pagerView: FSPagerView) {
        
    }
    
}

