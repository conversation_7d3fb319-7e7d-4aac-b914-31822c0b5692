//
//  GiftWallMainVC.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import JXSegmentedView

struct GiftUserModel: SmartCodable {
    var collectionDegree: Float = 0.0
    var illuminatedAtlasCount: Int = 0
    var illuminatedGiftCount: Int = 0
    var nickName: String?
    var photoUrl: String?
}

class GiftWallMainVC: YinDBaseVC {
    
    var imNum: String?
    
    var seletedIndex: Int = 0
    
    // 当前选择状态：true为已点亮，false为未点亮
    private var isLightSelected: Bool = true
    
    lazy var userView: GiftUserView = {
        let v = GiftUserView()
        return v
    }()
    
    // 滑块切换容器
    private lazy var switchContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        view.layerCornerRadius = 12
        return view
    }()
    
    // 滑动指示器
    private lazy var sliderView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.init(hex: 0xFF4A6B)
        view.layerCornerRadius = 10
        return view
    }()
    
    // 已点亮标签
    private lazy var lightLabel: UILabel = {
        let label = UILabel()
        label.text = "已点亮"
        label.font = .regular(10)
        label.textColor = .white
        label.textAlignment = .center
        label.isUserInteractionEnabled = true
        return label
    }()
    
    // 未点亮标签
    private lazy var unlightLabel: UILabel = {
        let label = UILabel()
        label.text = "未点亮"
        label.font = .regular(10)
        label.textColor = .white.withAlphaComponent(0.6)
        label.textAlignment = .center
        label.isUserInteractionEnabled = true
        return label
    }()
    
    private var segmentedDataSource: JXSegmentedTitleDataSource = {
        var segmeDataSource = JXSegmentedTitleDataSource()
        segmeDataSource.titleNormalColor = .white.withAlphaComponent(0.6)
        segmeDataSource.titleSelectedColor = .white
        segmeDataSource.titleNormalFont = .regular(14)
        segmeDataSource.titleSelectedFont = .medium(16)
        segmeDataSource.isTitleColorGradientEnabled = true
        segmeDataSource.isItemSpacingAverageEnabled = false
        segmeDataSource.itemWidth = JXSegmentedViewAutomaticDimension
        segmeDataSource.titles = ["礼物", "礼物套系"]
        return segmeDataSource
    }()
    
    //初始化JXSegmentedView
    private lazy var segmentedView : JXSegmentedView = {
        let segmentedView = JXSegmentedView()
        segmentedView.delegate = self
        segmentedView.dataSource = self.segmentedDataSource
        segmentedView.listContainer = listContainerView
        let lineIndicator = JXSegmentedIndicatorLineView()
        lineIndicator.indicatorWidth = 12
        lineIndicator.indicatorHeight = 3
        lineIndicator.verticalOffset = 2
        lineIndicator.indicatorColor = .white
        lineIndicator.indicatorCornerRadius = 1
        segmentedView.indicators = [lineIndicator]
        
        return segmentedView
    }()
    
    // 列表视图高度封装的类
    private lazy var listContainerView: JXSegmentedListContainerView! = {
        let listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.listCellBackgroundColor = .clear
        return listContainerView
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        
        isNavigationBarVisible = false
        
        loadData()
        
        // 添加手势识别器
        let lightTap = UITapGestureRecognizer(target: self, action: #selector(lightTapped))
        lightLabel.addGestureRecognizer(lightTap)
        
        let unlightTap = UITapGestureRecognizer(target: self, action: #selector(unlightTapped))
        unlightLabel.addGestureRecognizer(unlightTap)
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        let imgV = UIImageView(image: UIImage(named: "giftWallBg"))
        view.addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let navView = UIView()
        view.addSubview(navView)
        navView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.safeAreaTopHeight)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        let titleImgV = UIImageView(image: UIImage(named: "gift_title"))
        navView.addSubview(titleImgV)
        titleImgV.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        let backButton = UIButton()
        backButton.addTarget(self, action: #selector(backAction))
        backButton.tx_Img("back_arrow_icon")
        navView.addSubview(backButton)
        backButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(15)
        }
        
        
        
        
        // 添加滑块和标签
        switchContainerView.addSubview(sliderView)
        switchContainerView.addSubview(lightLabel)
        switchContainerView.addSubview(unlightLabel)
        
        // 设置滑块初始位置
        sliderView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview().inset(2)
            make.width.equalTo(48)
        }
        
        // 设置标签位置
        lightLabel.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(50)
        }
        
        unlightLabel.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(50)
        }
        
        view.addSubview(userView)
        userView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.navigationBarHeight + 24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(96)
        }
        
        view.addSubview(segmentedView)
        view.addSubview(listContainerView)
        
        segmentedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(userView.snp.bottom).offset(18)
            make.height.equalTo(40)
        }
        
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom).offset(10)
            make.left.right.bottom.equalToSuperview()
        }
        
        // 添加切换容器
        view.addSubview(switchContainerView)
        switchContainerView.snp.makeConstraints { make in
            make.centerY.equalTo(segmentedView)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(100)
            make.height.equalTo(24)
        }
        
    }
    
    @objc
    func backAction() {
        self.navigationController?.popViewController()
    }
    
    // MARK: - 标签点击事件
    @objc
    private func lightTapped() {
        // 如果已经是选中状态，不处理
        guard !isLightSelected else { return }
        
        isLightSelected = true
        animateSlider(toRight: false)
        updateLabelsColor()
        updateGiftWallData()
    }
    
    @objc
    private func unlightTapped() {
        // 如果已经是选中状态，不处理
        guard isLightSelected else { return }
        
        isLightSelected = false
        animateSlider(toRight: true)
        updateLabelsColor()
        updateGiftWallData()
    }
    
    // MARK: - 滑块动画
    private func animateSlider(toRight: Bool) {
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut) {
            self.sliderView.snp.remakeConstraints { make in
                if toRight {
                    // 向右移动
                    make.right.top.bottom.equalToSuperview().inset(2)
                    make.width.equalTo(48)
                } else {
                    // 向左移动
                    make.left.top.bottom.equalToSuperview().inset(2)
                    make.width.equalTo(48)
                }
            }
            self.switchContainerView.layoutIfNeeded()
        }
    }
    
    // MARK: - 更新标签颜色
    private func updateLabelsColor() {
        if isLightSelected {
            lightLabel.textColor = .white
            unlightLabel.textColor = .white.withAlphaComponent(0.6)
        } else {
            unlightLabel.textColor = .white
            lightLabel.textColor = .white.withAlphaComponent(0.6)
        }
    }
    
    // MARK: - 更新礼物墙数据
    private func updateGiftWallData() {
        // 获取当前的GiftWallListVC实例并更新数据
        if let giftWallVC = getCurrentGiftWallListVC() {
            giftWallVC.isLight = isLightSelected
            giftWallVC.collectionView.reloadData()
        }
    }
    
    // MARK: - 获取当前的GiftWallListVC实例
    private func getCurrentGiftWallListVC() -> GiftWallListVC? {
        // 只有在礼物tab时才有GiftWallListVC
        guard segmentedView.selectedIndex == 0 else { return nil }
        
        let validListDict = listContainerView.validListDict
        if let giftWallVC = validListDict[0] as? GiftWallListVC {
            return giftWallVC
        }
        return nil
    }
    
    override func loadData() {
        
        NetworkUtility.request(target: .userGiftDataInfo(["imNumber": imNum ?? ""]), model: GiftUserModel.self) { result in
            if result.isError { return }
            self.userView.userModel = result.model
        }
        
    }

}
/// 列表视图数据源
extension GiftWallMainVC: JXSegmentedListContainerViewDataSource, JXSegmentedViewDelegate {
    
    // 返回列表的数量
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return segmentedDataSource.titles.count
    }
    
    // 返回遵从协议的实例
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        
        if index == 1 {
            let vc = GiftSetListVC()
            vc.imID = imNum
            return vc
        }
        
        let vc = GiftWallListVC()
        vc.imID = imNum
        vc.isLight = isLightSelected  // 设置初始状态
        return vc
    }
    
    // MARK: - JXSegmentedViewDelegate
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        self.switchContainerView.isHidden = index == 1
    }
}
