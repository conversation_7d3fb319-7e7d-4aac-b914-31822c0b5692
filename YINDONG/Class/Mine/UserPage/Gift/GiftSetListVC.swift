//
//  GiftSetListVC.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import JXSegmentedView

class GiftSetListVC: YinDBaseVC {
    
    var imID: String?
    
    var datas: [GiftSetModel] = []

    override func viewDidLoad() {
        super.viewDidLoad()

        loadData()
    }
    

    override func lw_setupUI() {
        super.lw_setupUI()
        
        view.backgroundColor = .clear
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.register(cellWithClass: GiftSetTabCell.self)
        
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
    }
    
    override func loadData() {
        
        NetworkUtility.request(target: .userGiftAtlasInfoList(["imId": imID ?? ""]), model: GiftSetModel.self, isList: true) { result in
            if result.isError { return }
            
            self.datas = result.modelArr
            
            self.tableView.reloadData()
            
        }
    }
 

}

extension GiftSetListVC: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withClass: GiftSetTabCell.self)
        cell.model = datas[indexPath.row]
        return cell
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
     
        return datas.count
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 210
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let data = datas[indexPath.row]
        
        let vc = GiftSetBannerVC()
        vc.model = data
        vc.navigationTitle = data.atlasName
        navigationController?.pushViewController(vc)
    }
}

extension GiftSetListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
