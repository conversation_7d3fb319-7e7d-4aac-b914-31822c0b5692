//
//  GiftSetBannerVC.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import FSPagerView

class GiftSetBannerVC: YinDBaseVC {
    
    var datas: [DetailsListModel] {
        return model?.detailsList ?? []
    }
    
    var model: GiftSetModel?
    
    lazy var banner: FSPagerView = {
        let banner = FSPagerView()
        banner.backgroundColor = .clear
        banner.delegate = self
        banner.dataSource = self
        banner.layerCornerRadius = 10
        banner.automaticSlidingInterval = 0
        banner.transformer = FSPagerViewTransformer(type: .linear)
        banner.register(GiftSetBannerCell.self, forCellWithReuseIdentifier: "cell")
        banner.itemSize = CGSizeMake(291, 432)
        return banner
    }()
    
    lazy var yylab: YYLabel = {
        let lab = YYLabel()
        return lab
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        navigationTitleColor = .white
        navigationBarTintColor = .white
        navigationBarAlpha = 0
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        let imgV = UIImageView(image: UIImage(named: "giftWallBg"))
        view.addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        
        view.addSubview(banner)
        banner.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(482)
        }
        
        view.addSubview(yylab)
        yylab.snp.makeConstraints { make in
            make.bottom.equalTo(-(AppTool.safeAreaBottomHeight + 20))
            make.left.right.equalToSuperview().inset(20)
        }
        
        yylab.attributedText = makeRewardAttrText(from: model?.atlasPriceList ?? [])
    }

    /// 生成最终的 NSAttributedString
    func makeRewardAttrText(from list: [AtlasPriceListModel]) -> NSAttributedString {
        
        if list.count == 0 { return NSMutableAttributedString(string: "") }
        
        let font = UIFont.regular(12)
        let text = NSMutableAttributedString(string: "集齐套系可获得奖励  ")
        text.yy_font = font
        
        text.yy_color = .white.withAlphaComponent(0.87)
        
        // 遍历列表
        for (index, model) in list.enumerated() {
            
            let imgView = UIImageView()
            imgView.contentMode = .scaleAspectFit
            imgView.clipsToBounds = true
            imgView.setImage(from: model.priceUrl)
            
            // 建议统一尺寸，保证排版整齐
            let imgSize = CGSize(width: 29, height: 29)
            imgView.frame = CGRect(origin: .zero, size: imgSize)
            
            // ② 把 UIView 包成 YYTextAttachment
            let imgAttachment = NSMutableAttributedString.yy_attachmentString(
                withContent: imgView,
                contentMode: .center,
                attachmentSize: imgSize,
                alignTo: font,
                alignment: .center
            )
            text.append(imgAttachment)
            
            // ③ 加空格 + priceComments
            if let c = model.priceComments {
                let att = NSMutableAttributedString(string: " \(c) ")
                att.yy_font = font
                att.yy_color = .white.withAlphaComponent(0.87)
                text.append(att)
            }
        }
        text.yy_alignment = .center
        return text
    }
    
}

extension GiftSetBannerVC: FSPagerViewDelegate, FSPagerViewDataSource {
    
    func numberOfItems(in pagerView: FSPagerView) -> Int {
        return self.datas.count
    }
    
    func pagerView(_ pagerView: FSPagerView, cellForItemAt index: Int) -> FSPagerViewCell {
        let cell = pagerView.dequeueReusableCell(withReuseIdentifier: "cell", at: index)
        if let cell = cell as? GiftSetBannerCell {
            cell.model = self.datas[index]
        }

        return cell
    }
    
    func pagerViewDidScroll(_ pagerView: FSPagerView) {
        
    }
    
}


