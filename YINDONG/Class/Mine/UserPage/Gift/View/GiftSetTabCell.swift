//
//  GiftSetTabCell.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import Lottie

class GiftSetTabCell: UITableViewCell {
    
    var model: GiftSetModel? {
        didSet {
            guard let model = model else { return }
            updateUI(with: model)
            
            DotLottieFile.named("giftbook_light") { result in
                switch result {
                case .success(let file):
                    self.lottieAnimationView.loadAnimation(from: file)
                    self.lottieAnimationView.loopMode = .loop
                    self.lottieAnimationView.play()
                case .failure(let error):
                    break
                }
            }
        }
    }
    
    // MARK: - UI Components
    lazy var bgImgV: UIImageView = {
        let imgV = UIImageView(image: UIImage(named: "giftCell_bg"))
        return imgV
    }()
    
    lazy var lottieAnimationView: LottieAnimationView = {
        let view = LottieAnimationView() // Initialize without frame initially
        view.loopMode = .loop
        view.isUserInteractionEnabled = false
        //        view.isHidden = true
        view.backgroundBehavior = .pauseAndRestore
        return view
    }()
    
    
    /// 套系标题
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = .init(hex: 0xF3CB7D)
        label.textAlignment = .center
        return label
    }()
    
    /// 收集进度标签
    private lazy var progressLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = UIColor(hex: 0xFFE286)
        label.textAlignment = .right
        return label
    }()
    
    /// 顶部装饰图片
    private lazy var topDecorationView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .equalSpacing
        stackView.alignment = .center
        stackView.spacing = 8
        return stackView
    }()
    
    /// 左侧装饰图片
    private lazy var leftDecoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// 右侧装饰图片
    private lazy var rightDecoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// 礼物集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 12
        layout.minimumLineSpacing = 12
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(GiftSetItemCell.self, forCellWithReuseIdentifier: "GiftSetItemCell")
        return collectionView
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup UI
    private func setupUI() {
        selectionStyle = .none
        backgroundColor = .clear
        
        contentView.addSubview(lottieAnimationView)
        contentView.addSubview(bgImgV)
        
        contentView.addSubview(topDecorationView)
        contentView.addSubview(progressLabel)
        contentView.addSubview(collectionView)
        
        // 添加装饰图片到 StackView
        topDecorationView.addArrangedSubview(leftDecoImageView)
        topDecorationView.addArrangedSubview(titleLabel) // 占位视图
        topDecorationView.addArrangedSubview(rightDecoImageView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        bgImgV.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(38)
            make.left.right.equalToSuperview().inset(9)
            make.top.equalTo(10)
        }
        
        lottieAnimationView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        topDecorationView.snp.makeConstraints { make in
            make.top.equalTo(bgImgV).offset(15)
            make.centerX.equalToSuperview()
            make.height.equalTo(21)
        }

        progressLabel.snp.makeConstraints { make in
            make.centerY.equalTo(topDecorationView)
            make.right.equalTo(bgImgV)
        }
        
        collectionView.snp.makeConstraints { make in
            make.left.right.equalTo(bgImgV).inset(16)
            make.top.equalTo(topDecorationView.snp.bottom).offset(15)
            make.height.equalTo(98)
        }
        
        leftDecoImageView.image = UIImage(named: "left_001")
        rightDecoImageView.image = UIImage(named: "left_002")
    }
    
    // MARK: - Update UI
    private func updateUI(with model: GiftSetModel) {
        // 设置套系名称
        titleLabel.text = model.atlasName
        
        // 设置收集进度
        let collectedCount = model.userNum
        let totalCount = model.atlasNum
        let isCompleted = collectedCount >= totalCount
        progressLabel.text = isCompleted ? "\(collectedCount)/\(totalCount) 已集齐" : "\(collectedCount)/\(totalCount) 未集齐"
        progressLabel.textColor = isCompleted ? UIColor(hex: 0x00FF88) : UIColor(hex: 0xFFE286)
        
        topDecorationView.snp.updateConstraints { make in
            make.top.equalTo(bgImgV).offset( isCompleted ? 24 : 15)
        }
        
        lottieAnimationView.isHidden = !isCompleted
        bgImgV.isHidden = isCompleted
        
        // 刷新集合视图
        collectionView.reloadData()
    }

}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate
extension GiftSetTabCell: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return model?.detailsList.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "GiftSetItemCell", for: indexPath) as! GiftSetItemCell
        
        if let detailItem = model?.detailsList[indexPath.item] {
            cell.configure(with: detailItem)
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 60, height: 98)
    }
}

// MARK: - GiftSetItemCell
class GiftSetItemCell: UICollectionViewCell {
    
    /// 卡片背景
    private lazy var cardBackgroundView: UIImageView = {
        let view = UIImageView()
        return view
    }()
    
    /// 礼物图片
    private lazy var giftImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 6
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    /// 礼物名称
    private lazy var giftNameLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(10)
        label.textColor = .init(hex: 0xFBF0E0)
        label.textAlignment = .center
        label.numberOfLines = 2
        return label
    }()
    
    /// 状态标签 (未拥有/已拥有)
    private lazy var statusBadge: UILabel = {
        let label = UILabel()
        label.font = .regular(10)
        label.textColor = UIColor(hex: 0x999999)
        label.textAlignment = .center
        label.text = "未拥有"
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        contentView.addSubview(cardBackgroundView)
        cardBackgroundView.addSubview(giftImageView)
        cardBackgroundView.addSubview(giftNameLabel)
        cardBackgroundView.addSubview(statusBadge)
        
        cardBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        giftImageView.snp.makeConstraints { make in
            make.top.equalTo(7)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(52)
        }
        
        giftNameLabel.snp.makeConstraints { make in
            make.top.equalTo(giftImageView.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(4)
        }
        
        statusBadge.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-5)
            make.centerX.equalToSuperview()
        }
    }
    
    func configure(with item: DetailsListModel) {
        // 判断是否拥有该礼物
        let hasGift = isValid(item.num) && item.num ?? 0 > 0
        
        // 设置礼物名称
        giftNameLabel.text = item.giftName ?? ""
        
        // 设置数量标签
        if let num = item.num, hasGift {
            statusBadge.text = "x\(num)"
            statusBadge.textColor = .init(hex: 0xFBF0E0)
        } else {
            statusBadge.text = "未拥有"
            statusBadge.textColor = .init(hex: 0x989898)
        }
        
        // 设置背景 - 根据拥有状态选择不同背景
        if hasGift {
            cardBackgroundView.setImage(from: "at_selAl")
        } else {
            cardBackgroundView.setImage(from: "at_nselAl")
        }
        
        // 根据拥有状态调整视觉效果
        if hasGift {
            giftNameLabel.textColor = .white
            giftImageView.setImage(from: item.giftUrl)
        } else {
            giftNameLabel.textColor = UIColor(hex: 0x999999)
            giftImageView.blackImage(from: item.giftUrl)
        }
    }
}
