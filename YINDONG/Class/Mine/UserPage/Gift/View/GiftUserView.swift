//
//  GiftUserView.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import SnapKit
import Kingfisher

class GiftUserView: UIView {

    // MARK: - UI Components
    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 30
        imageView.layer.masksToBounds = true
        imageView.backgroundColor = UIColor.lightGray
        return imageView
    }()
    
    private let nicknameLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.text = "我是昵称"
        return label
    }()
    
    private let dataStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 20
        return stackView
    }()
    
    private let giftCountView = DataItemView(title: "已点亮礼物")
    private let atlasCountView = DataItemView(title: "已点亮系列")
    private let collectionView = DataItemView(title: "收集度")

    var userModel: GiftUserModel? {
        didSet {
            guard let userModel = userModel else { return }
            updateUI(with: userModel)
        }
    }
    
    convenience init() {
        self.init(frame: .zero)
        setupUI()
    }

    
    // MARK: - Setup UI
    private func setupUI() {
        backgroundColor = .white.withAlphaComponent(0.1)
        layerBorderColor = .white.withAlphaComponent(0.1)
        layerBorderWidth = 1
        layerCornerRadius = 16
        
        addSubview(avatarImageView)
        addSubview(nicknameLabel)
        addSubview(dataStackView)
        
        dataStackView.addArrangedSubview(giftCountView)
        dataStackView.addArrangedSubview(atlasCountView)
        dataStackView.addArrangedSubview(collectionView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(60)
        }
        
        nicknameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(15)
            make.top.equalToSuperview().offset(15)
            make.right.equalToSuperview().offset(-20)
        }
        
        dataStackView.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(15)
            make.top.equalTo(nicknameLabel.snp.bottom).offset(10)
            make.right.equalToSuperview().offset(-20)
            make.bottom.equalToSuperview().offset(-15)
        }
    }
    
    // MARK: - Update UI
    private func updateUI(with model: GiftUserModel) {
        // 更新昵称
        nicknameLabel.text = model.nickName ?? "我是昵称"
        
        avatarImageView.setImage(from: model.photoUrl)
        
        // 更新数据
        giftCountView.updateData(value: "\(model.illuminatedGiftCount)")
        atlasCountView.updateData(value: "\(model.illuminatedAtlasCount)")
        collectionView.updateData(value: "\(Int(model.collectionDegree))%")
    }
}

// MARK: - DataItemView
private class DataItemView: UIView {
    
    private let valueLabel: UILabel = {
        let label = UILabel()
        label.textColor = .init(hex: 0xFFEABE)
        label.font = .medium(16)
        return label
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white.withAlphaComponent(0.6)
        label.font = .regular(10)
        return label
    }()
    
    private let stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 5
        stackView.alignment = .leading
        return stackView
    }()
    
    init(title: String) {
        super.init(frame: .zero)
        titleLabel.text = title
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        addSubview(stackView)
        
        stackView.addArrangedSubview(valueLabel)
        stackView.addArrangedSubview(titleLabel)
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func updateData(value: String) {
        valueLabel.text = value
    }
}
