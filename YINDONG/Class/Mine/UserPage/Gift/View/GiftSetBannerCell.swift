//
//  GiftSetBannerCell.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import FSPagerView
import Lottie

class GiftSetBannerCell: FSPagerViewCell {
    
    var model: DetailsListModel? {
        didSet {
            guard let model = model else { return }
            updateUI(with: model)
        }
    }

    private func updateUI(with model: DetailsListModel) {
        // 设置卡片背景和礼物图片
        if let num = model.num, num > 0 {
            bgimV.setImage(from: model.cardBackground)
            topImgV.setImage(from: model.giftUrl)
            coinImageView.image = UIImage(named: "zuan_coinicon")
        } else {
            bgimV.setImage(from: model.cardBackgroundNotLit)
            topImgV.blackImage(from: model.giftUrl)
            coinImageView.image = UIImage(named: "zuan_coinicon")?.grayCI()
        }

        // 设置等级图标
        tipImgV.image = UIImage(named: "\(model.labelLevel ?? "")_icon")

        // 设置动画效果
        if model.labelLevel == "SSR" {
            lottieAnimationView.isHidden = false
            DotLottieFile.named("giftbook_card_light") { result in
                switch result {
                case .success(let file):
                    self.lottieAnimationView.loadAnimation(from: file)
                    self.lottieAnimationView.loopMode = .loop
                    self.lottieAnimationView.play()
                case .failure(let error):
                    break
                }
            }
        } else {
            lottieAnimationView.stop()
            lottieAnimationView.isHidden = true
        }

        // 设置底部信息
        updateBottomInfo(with: model)
    }

    private func updateBottomInfo(with model: DetailsListModel) {
        // 设置礼物名称
        giftNameValueLabel.text = model.giftName ?? "未知礼物"

        // 设置礼物价值
        let giftValue = model.giftAmount
        giftValueValueLabel.text = String(format: "%.0f", giftValue)

        // 设置点亮次数
        let lightCount = model.num ?? 0
        lightCountValueLabel.text = "\(lightCount)"

        // 根据是否拥有调整底部信息的透明度
        let hasGift = lightCount > 0
        let alpha: CGFloat = hasGift ? 1.0 : 0.6

        giftNameValueLabel.alpha = alpha
        giftValueValueLabel.alpha = alpha
        lightCountValueLabel.alpha = alpha
        coinImageView.alpha = alpha
    }
    
    lazy var bgimV: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()
    
    lazy var lottieAnimationView: LottieAnimationView = {
        let view = LottieAnimationView() // Initialize without frame initially
        view.loopMode = .loop
        view.contentMode = .scaleAspectFill
        view.isUserInteractionEnabled = false
        view.backgroundBehavior = .pauseAndRestore
        return view
    }()
    
    
    lazy var tipImgV: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()
    
    lazy var topImgV: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()

    // MARK: - 底部信息区域
    /// 分割线
    private lazy var separatorLine: UIImageView = {
        let line = UIImageView(image: UIImage(named: "line_imgV")?.createResizableImageCentered())
        
        return line
    }()

    /// 礼物名称标签
    private lazy var giftNameLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(13)
        label.textColor = .white.withAlphaComponent(0.6)
        label.text = "礼物名称："
        return label
    }()

    /// 礼物名称值
    private lazy var giftNameValueLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(13)
        label.textColor = .white
        return label
    }()

    /// 礼物价值标签
    private lazy var giftValueLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(13)
        label.textColor = .white.withAlphaComponent(0.6)
        label.text = "礼物价值："
        return label
    }()

    /// 礼物价值值（包含金币图标）
    private lazy var giftValueContainer: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 4
        stackView.alignment = .center
        return stackView
    }()

    private lazy var coinImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "zuan_coinicon")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private lazy var giftValueValueLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(13)
        label.textColor = UIColor(hex: 0xFFD700) // 金色
        return label
    }()

    /// 点亮次数标签
    private lazy var lightCountLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(13)
        label.textColor = .white.withAlphaComponent(0.6)
        label.text = "点亮次数："
        return label
    }()

    /// 点亮次数值
    private lazy var lightCountValueLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(13)
        label.textColor = .white
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: .zero)
        setupUI()
    }

    private func setupUI() {
        // 添加主要视图
        contentView.addSubview(lottieAnimationView)
        contentView.addSubview(bgimV)
        contentView.addSubview(topImgV)
        contentView.addSubview(tipImgV)

        // 添加底部信息区域
        contentView.addSubview(separatorLine)
        contentView.addSubview(giftNameLabel)
        contentView.addSubview(giftNameValueLabel)
        contentView.addSubview(giftValueLabel)
        contentView.addSubview(giftValueContainer)
        contentView.addSubview(lightCountLabel)
        contentView.addSubview(lightCountValueLabel)

        // 设置价值容器
        giftValueContainer.addArrangedSubview(coinImageView)
        giftValueContainer.addArrangedSubview(giftValueValueLabel)

        setupConstraints()
    }

    private func setupConstraints() {
        lottieAnimationView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(1.35)
        }

        bgimV.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        topImgV.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(50)
            make.width.height.equalTo(217)
        }

        tipImgV.snp.makeConstraints { make in
            make.right.equalTo(-22)
            make.top.equalTo(23)
        }

        // 底部信息区域约束
        separatorLine.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalTo(-127)
            make.height.equalTo(1)
        }

        giftNameLabel.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(separatorLine.snp.bottom).offset(16)
        }

        giftNameValueLabel.snp.makeConstraints { make in
            make.left.equalTo(giftNameLabel.snp.right).offset(8)
            make.centerY.equalTo(giftNameLabel)
        }

        giftValueLabel.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(giftNameLabel.snp.bottom).offset(12)
        }

        giftValueContainer.snp.makeConstraints { make in
            make.left.equalTo(giftValueLabel.snp.right).offset(8)
            make.centerY.equalTo(giftValueLabel)
        }

        coinImageView.snp.makeConstraints { make in
            make.width.height.equalTo(16)
        }

        lightCountLabel.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(giftValueLabel.snp.bottom).offset(12)
        }

        lightCountValueLabel.snp.makeConstraints { make in
            make.left.equalTo(lightCountLabel.snp.right).offset(8)
            make.centerY.equalTo(lightCountLabel)
        }
    }
    
    
    
    @MainActor required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}
