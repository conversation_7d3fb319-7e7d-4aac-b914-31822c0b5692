//
//  GiftWallBaseModel.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit

struct GiftWallBaseModel: SmartCodable {
    var userGoodsGiftCollectedList = [GiftCollectModel]()
    var userGoodsGiftList = [GiftCollectModel]()
    
    /// 根据 name 优先级「1 → 3 → 0 → 2 → 其他」排序
    /// 调用示例：
    /// var wall = fetchedModel          // 网络解码回来
    /// wall.loadData()                  // 列表已排好序
    /// tableView.reloadData()
    mutating func loadData() {
        
        // ① 定义优先级
        func rank(for name: String?) -> Int {
            switch name {
            case "1": return 0          // 最高
            case "3": return 1
            case "0": return 2
            case "2": return 3
            default:  return 4          // 其他
            }
        }
        
        // ② 通用排序闭包：先比 rank，再比字典序
        let sortRule: (GiftCollectModel, GiftCollectModel) -> Bool = { lhs, rhs in
            let lRank = rank(for: lhs.name)
            let rRank = rank(for: rhs.name)
            if lRank == rRank {
                return (lhs.name ?? "") < (rhs.name ?? "")
            }
            return lRank < rRank
        }
        
        // ③ 就地排序两组列表
        userGoodsGiftCollectedList.sort(by: sortRule)
        userGoodsGiftList.sort(by: sortRule)
    }
}

struct GiftCollectModel: SmartCodable {
    var name: String?
    var value = [GiftModel]()
}
