//
//  GiftSetModel.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit

class GiftSetModel: SmartCodable {
    var atlasBseDrawing: String?
    var atlasCover: String?
    var atlasName: String?
    var atlasNum: Int = 0
    var atlasPriceList = [AtlasPriceListModel]()
    var atlasType: Int = 0
    var atlasTypeUrl: String?
    var baseDrawingGathering: String?
    var detailsList = [DetailsListModel]()
    var endDate: Int = 0
    var sort: Int = 0
    var startDate: Int = 0
    var userNum: Int = 0
    
    required init() {
        
    }
    
}


struct AtlasPriceListModel: SmartCodable {
    var atlasId: Int = 0
    var createBy: Int = 0
    var createDate: Int = 0
    var id: Int = 0
    var isDel: Int = 0
    var priceComments: String?
    var priceCount: Int = 0
    var priceId: Int = 0
    var priceName: String?
    var priceSubscriptUrl: String?
    var priceType: Int = 0
    var priceUrl: String?
    var sort: Int = 0
    var updateBy: Int = 0
    var updateDate: Int = 0
}

struct DetailsListModel: SmartCodable {
    var cardBackground: String?
    var cardBackgroundNotLit: String?
    var cardGiftUrl: String?
    var giftAmount: Float = 0.0
    var giftName: String?
    var giftUrl: String?
    var labelLevel: String?
    var labelUrl: String?
    var num: Int?
}
