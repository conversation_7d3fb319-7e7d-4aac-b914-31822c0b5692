//
//  GiftWallListVC.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit
import JXSegmentedView
import SnapKit

// MARK: - Gift Wall Cell
class GiftWallCell: UICollectionViewCell {
    static let identifier = "GiftWallCell"
    
    var isLight: Bool = true
    
    private let iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        return imageView
    }()
    
    private let nameLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = .white.withAlphaComponent(0.87)
        label.textAlignment = .center
        label.numberOfLines = 1
        return label
    }()
    
    private let countLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(10)
        label.textColor = .white.withAlphaComponent(0.6)
        label.textAlignment = .center
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(iconImageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(countLabel)
        
        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(2)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(67)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(4)
        }
        
        countLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(2)
            make.left.right.equalToSuperview().inset(4)
        }
    }
    
    func configure(with gift: GiftModel) {
        nameLabel.text = gift.name ?? gift.giftName
        if isLight {
            countLabel.text = "\(gift.giftNum)"
            iconImageView.setImage(from: gift.quietUrl ?? gift.giftQuietUrl)
            
            countLabel.font = .regular(10)
            countLabel.textColor = .white.withAlphaComponent(0.6)
        } else {
            countLabel.font = .medium(12)
            countLabel.textColor = .init(hex: 0xFFEABE)
            countLabel.text = "未拥有"
            iconImageView.blackImage(from: gift.quietUrl)
        }
    }
}

class GiftWallListVC: YinDBaseVC {
    
    var imID: String?
    
    var model: GiftWallBaseModel?
    
    var isLight: Bool = true
    
    // 获取当前的数据源 - 根据有无收集数据选择显示哪个列表
    var currentDataSource: [GiftCollectModel] {
        let collectedList = model?.userGoodsGiftCollectedList ?? []
        let allList = model?.userGoodsGiftList ?? []
        return isLight ? allList : collectedList
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        loadData()
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        view.backgroundColor = .clear
        
        collectionView.register(GiftWallCell.self, forCellWithReuseIdentifier: GiftWallCell.identifier)
        collectionView.register(
            SectionHeaderView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
            withReuseIdentifier: SectionHeaderView.identifier
        )
        collectionView.dataSource = self
        collectionView.delegate = self
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    override func configLayout() -> UICollectionViewFlowLayout {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 0
        layout.minimumLineSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        return layout
    }

    override func loadData() {
        NetworkUtility.request(target: .userGiftCollect(["imId": imID ?? ""]), model: GiftWallBaseModel.self) { result in
            if result.isError { return }
            self.model = result.model
            self.model?.loadData()
            self.collectionView.reloadData()
        }
    }
    
    // 获取section标题
    func getSectionTitle(for name: String?) -> String {
        switch name {
        case "1": return "史诗礼物"
        case "3": return "限量礼物"
        case "4": return "戒指"
        case "2": return "假面礼物"
        default: return "普通礼物"
        }
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate
extension GiftWallListVC: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    // MARK: - Section Header Implementation
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        if kind == UICollectionView.elementKindSectionHeader {
            let headerView = collectionView.dequeueReusableSupplementaryView(
                ofKind: kind,
                withReuseIdentifier: SectionHeaderView.identifier,
                for: indexPath
            ) as! SectionHeaderView
            
            let section = currentDataSource[indexPath.section]
            headerView.title = getSectionTitle(for: section.name)
            headerView.titleLabel.font = .regular(14)
            headerView.titleLabel.textColor = .white.withAlphaComponent(0.87)
            return headerView
        }
        return UICollectionReusableView()
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        return CGSize(width: collectionView.frame.width, height: 40)
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return currentDataSource.count
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return currentDataSource[section].value.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: GiftWallCell.identifier, for: indexPath) as! GiftWallCell
        let gift = currentDataSource[indexPath.section].value[indexPath.item]
        cell.isLight = isLight
        cell.configure(with: gift)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 2x2 网格布局，计算每个cell的宽度
        let sectionInset = (collectionViewLayout as! UICollectionViewFlowLayout).sectionInset
        let interitemSpacing = (collectionViewLayout as! UICollectionViewFlowLayout).minimumInteritemSpacing
        
        let totalWidth = collectionView.frame.width - sectionInset.left - sectionInset.right - interitemSpacing
        let cellWidth = totalWidth / 4.0
        let cellHeight: CGFloat = 118 // 根据设计稿调整高度
        
        return CGSize(width: cellWidth, height: cellHeight)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        // 处理礼物点击事件
        let gift = currentDataSource[indexPath.section].value[indexPath.item]
        // TODO: 添加礼物详情展示逻辑
    }
}

extension GiftWallListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
