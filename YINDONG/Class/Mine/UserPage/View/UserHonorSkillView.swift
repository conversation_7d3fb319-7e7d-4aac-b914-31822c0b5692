//
//  UserHonorSkillView.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import SnapKit


class UserHonorSkillView: UIView {
    
    // MARK: - Properties
    
    /// 技能数据
    var skillItems: [SkillLevelModel] = [] {
        didSet {
            updateSkillItems()
        }
    }
    
    /// 轮播消息数据
    var scrollMessages: [SkillAttackedRecordModel] = [] {
        didSet {
            updateScrollMessages()
        }
    }
    
    /// 轮播定时器
    private var scrollTimer: Timer?
    
    // MARK: - UI Components
    
    /// 主容器StackView
    private lazy var containerStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "技能"
        label.font = .medium(16)
        label.textColor = UIColor.init(hex: 0x333333)
        return label
    }()

    /// 技能容器视图 - 使用Frame布局
    private lazy var skillsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 轮播容器
    private lazy var scrollContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.init(hex: 0xF8F9FA)
        view.layer.cornerRadius = 8
        view.layer.masksToBounds = true
        view.isHidden = true
        return view
    }()
    
    /// 轮播TableView
    private lazy var scrollTableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.isScrollEnabled = false
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ScrollMessageCell.self, forCellReuseIdentifier: "ScrollMessageCell")
        return tableView
    }()
    
    /// 技能项视图数组
    var skillItemViews: [UIView] = []
    
    var skillRatViews: [SkillLevelRatingView] = []
    
    /// 当前轮播索引
    private var currentScrollIndex = 0
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupDefaultData()
        self.setTap()

    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        
    }
    
    func setTap() {
        for (i, view) in self.skillItemViews.enumerated() {
            view.tag = i
            view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(skillAction)))
        }
    }
    
    deinit {
        stopScrollTimer()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = 12
        layer.masksToBounds = true
        layerBorderColor = .eColor
        layerBorderWidth = 0.5
        
        // 添加主容器
        addSubview(containerStackView)
        containerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        // 添加标题
        containerStackView.addArrangedSubview(titleLabel)
        
        // 添加技能容器
        containerStackView.addArrangedSubview(skillsContainer)
        skillsContainer.snp.makeConstraints { make in
            make.height.equalTo(115) // 调整高度：30*3(三行) + 15*2(行间距) = 120，稍微小一点105
        }
        
        // 添加轮播容器
        containerStackView.addArrangedSubview(scrollContainer)
        setupScrollContainer()
        
   
    }
    
    @objc
    func skillAction(tap: UITapGestureRecognizer) {
        guard let tag = tap.view?.tag else { return }
        
        let vv = SkillDescPopView()
        vv.index = tag
        vv.datas = skillItems
        vv.show()
        
    }
    
    private func setupScrollContainer() {
        scrollContainer.addSubview(scrollTableView)
        scrollTableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置轮播容器高度
        scrollContainer.snp.makeConstraints { make in
            make.height.equalTo(40)
        }
    }
    
    // MARK: - Default Data
    
    private func setupDefaultData() {
        // 设置默认技能数据（5个技能）
        skillItems = [
            SkillLevelModel(),
            SkillLevelModel(),
            SkillLevelModel(),
            SkillLevelModel(),
            SkillLevelModel()
        ]
        
        // 设置默认轮播消息
        scrollMessages = []
    }
    
    // MARK: - Skill Items Layout
    
    private func updateSkillItems() {
        let targetCount = min(skillItems.count, 5)
        
        // 布局参数：每行2个
        let containerWidth = UIScreen.main.bounds.width - 64  // 减去左右边距
        let itemSpacing: CGFloat = 10  // 两个item之间的间距
        let itemWidth = (containerWidth - itemSpacing) / 2  // 动态计算每个item的宽度
        let itemHeight: CGFloat = 30  // 技能项高度
        
        // 调整视图数量：不够就创建，多了就隐藏
        while skillItemViews.count < targetCount {
            let newView = createSkillItem(skill: SkillLevelModel())
            skillsContainer.addSubview(newView)
            skillItemViews.append(newView)
        }
        
        // 更新每个技能视图
        for (index, skill) in skillItems.prefix(5).enumerated() {
            let skillView = skillItemViews[index]
            
            // 更新技能内容
            updateSkillView(skillView, with: skill)
            
            // 计算位置：每行2个
            let row = index / 2      // 行号：0, 0, 1, 1, 2
            let col = index % 2      // 列号：0, 1, 0, 1, 0
            
            let x = CGFloat(col) * (itemWidth + itemSpacing)
            let y = CGFloat(row) * (itemHeight + 15)  // 行间距15
            
            skillView.frame = CGRect(x: x, y: y, width: itemWidth, height: itemHeight)
            skillView.isHidden = false
        }
        
        // 隐藏多余的视图
        for i in targetCount..<skillItemViews.count {
            skillItemViews[i].isHidden = true
        }
    }
    
    private func createSkillItem(skill: SkillLevelModel) -> UIView {
        let container = UIView()
        
        let imageView = UIImageView()
        imageView.image = UIImage(named: skill.skillType.imgName)
        imageView.contentMode = .scaleAspectFit
        
        let levelStarView = SkillLevelRatingView()
        levelStarView.configureAndPlay(withLevel: skill.allLevel)
        container.addSubview(imageView)
        container.addSubview(levelStarView)
        
        skillRatViews.append(levelStarView)
        
        imageView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        levelStarView.snp.makeConstraints { make in
            make.left.equalTo(imageView.snp.right).offset(4)
            make.centerY.equalTo(imageView)
            make.right.equalToSuperview()
            make.width.equalTo(100)
            make.height.equalTo(24)
        }
        
        return container
    }
    
    private func updateSkillView(_ skillView: UIView, with skill: SkillLevelModel) {
        // 直接更新现有子视图，不重新创建
        if let imageView = skillView.subviews.first(where: { $0 is UIImageView }) as? UIImageView {
            imageView.image = UIImage(named: skill.skillType.imgName)
        }
        
        if let levelStarView = skillView.subviews.first(where: { $0 is SkillLevelRatingView }) as? SkillLevelRatingView {
            levelStarView.configureAndPlay(withLevel: skill.allLevel)
        }
    }
    
    // MARK: - Scroll Messages
    
    private func updateScrollMessages() {
        // 先停止定时器避免冲突
        stopScrollTimer()
        
        // 重置索引到合理位置
        currentScrollIndex = 0
        
        if scrollMessages.count == 0 {
            scrollContainer.isHidden = true
            return
        }
        
        scrollContainer.isHidden = false
        
        // 使用performBatchUpdates避免闪烁
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.scrollTableView.reloadData()
            
            // 如果有数据，滚动到初始位置（中间的某个位置，避免边界问题）
            if !self.scrollMessages.isEmpty {
                let middleIndex = self.scrollMessages.count * 500 // 中间位置
                let indexPath = IndexPath(row: middleIndex, section: 0)
                self.scrollTableView.scrollToRow(at: indexPath, at: .top, animated: false)
                self.currentScrollIndex = 0
            }
            
            // 重新启动定时器（如果有多条消息）
            if self.scrollMessages.count > 1 {
                self.startScrollTimer()
            }
        }
    }
    
    // MARK: - Timer Methods
    
    private func startScrollTimer() {
        stopScrollTimer()
        guard scrollMessages.count > 1 else { return }
        
        scrollTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { [weak self] _ in
            self?.scrollToNext()
        }
    }
    
    private func stopScrollTimer() {
        scrollTimer?.invalidate()
        scrollTimer = nil
    }
    
    private func scrollToNext() {
        guard scrollMessages.count > 1 else { return }
        
        // 获取当前可见的row
        let currentVisibleRow = scrollTableView.indexPathsForVisibleRows?.first?.row ?? 0
        let nextRow = currentVisibleRow + 1
        
        // 确保不会超出合理范围，如果接近边界就重置到中间
        let maxSafeRow = scrollMessages.count * 1500
        let minSafeRow = scrollMessages.count * 500
        
        let targetRow: Int
        if nextRow > maxSafeRow {
            // 重置到中间位置，保持相同的相对位置
            targetRow = minSafeRow + (currentVisibleRow % scrollMessages.count) + 1
        } else {
            targetRow = nextRow
        }
        
        let indexPath = IndexPath(row: targetRow, section: 0)
        
        // 使用更平滑的滚动，避免闪烁
        UIView.animate(withDuration: 0.5, delay: 0, options: [.curveEaseInOut], animations: {
            self.scrollTableView.scrollToRow(at: indexPath, at: .top, animated: false)
        }, completion: nil)
        
        // 更新逻辑索引
        currentScrollIndex = (currentScrollIndex + 1) % scrollMessages.count
    }
    
    // MARK: - Public Methods
    
    /// 设置技能数据
    /// - Parameter skills: 技能数组
    func setSkillItems(_ skills: [SkillLevelModel]) {
        skillItems = skills
    }
    
    /// 更新单个技能等级（性能更好）
    /// - Parameters:
    ///   - index: 技能索引
    ///   - level: 新等级
    func updateSkillLevel(at index: Int, level: Int) {
        guard index < skillItems.count && index < skillItemViews.count else { return }
        
        skillItems[index].skillLevel = level
        
        // 直接更新对应的视图，不重建整个列表
        let skillView = skillItemViews[index] 
        updateSkillView(skillView, with: skillItems[index])
    }
    
    /// 更新技能图标
    /// - Parameters:
    ///   - index: 技能索引  
    ///   - iconName: 新图标名称
    func updateSkillIcon(at index: Int, iconName: String) {
        guard index < skillItems.count && index < skillItemViews.count else { return }
        
        // 直接更新图标
        let skillView = skillItemViews[index]
        if let imageView = skillView.subviews.first(where: { $0 is UIImageView }) as? UIImageView {
            imageView.image = UIImage(named: iconName)
        }
    }
    
    /// 设置轮播消息
    /// - Parameter messages: 消息数组
    func setScrollMessages(_ messages: [SkillAttackedRecordModel]) {
        scrollMessages = messages
        if messages.count > 1 {
            startScrollTimer()
        }
    }
    
    /// 设置标题
    /// - Parameter title: 标题文本
    func setTitle(_ title: String) {
        titleLabel.text = title
    }
    
    /// 隐藏标题
    /// - Parameter isHidden: 是否隐藏
    func setTitleHidden(_ isHidden: Bool) {
        UIView.animate(withDuration: 0.3) {
            self.titleLabel.isHidden = isHidden
        }
    }
    
    /// 隐藏轮播区域
    /// - Parameter isHidden: 是否隐藏
    func setScrollContainerHidden(_ isHidden: Bool) {
        UIView.animate(withDuration: 0.3) {
            self.scrollContainer.isHidden = isHidden
        }
        
        if isHidden {
            stopScrollTimer()
        } else {
            startScrollTimer()
        }
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate

extension UserHonorSkillView: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        // 返回一个很大的数字实现无限循环
        return scrollMessages.isEmpty ? 0 : scrollMessages.count * 2000
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ScrollMessageCell", for: indexPath) as! ScrollMessageCell
        
        let actualIndex = indexPath.row % scrollMessages.count
        let message = scrollMessages[actualIndex]
        cell.configure(with: message)
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 40
    }
    
    func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
        // 移除重置逻辑避免闪烁，由scrollToNext方法自己处理边界情况
    }
}

// MARK: - ScrollMessageCell

private class ScrollMessageCell: UITableViewCell {
    
    // MARK: - UI Components
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 12
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private lazy var messageLabel: YYLabel = {
        let label = YYLabel()
        label.font = .regular(12)
        label.textColor = UIColor.init(hex: 0x666666)
        label.numberOfLines = 1
        return label
    }()
    
    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(avatarImageView)
        contentView.addSubview(messageLabel)
        
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        messageLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-12)
        }
    }
    
    // MARK: - Configuration
    
    func configure(with message: SkillAttackedRecordModel) {
        avatarImageView.setImage(from: message.skillType.imgName)
        messageLabel.attributedText = message.toYYAttributedString()
    }
}
