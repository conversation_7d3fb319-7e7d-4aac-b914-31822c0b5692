//
//  UserHonorVehicleView.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit

class UserHonorVehicleView: UIView {
    
    var datas: [DressBaseModel] = [] {
        didSet {
            giftCollectionView.reloadData()
        }
    }
    
    /// 礼物区域容器
    lazy var giftSection: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// 礼物标题容器
    lazy var giftTitleContainer: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 礼物标题
    lazy var giftTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "座驾"
        label.font = .medium(16)
        label.textColor = UIColor(hex: 0x333333)
        return label
    }()
    
    /// 普通礼物CollectionView
    lazy var giftCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(cellWithClass: HonOrGiftItemCell.self)
        return collectionView
    }()

    convenience init() {
        self.init(frame: .zero)
        backgroundColor = .white
        layer.cornerRadius = 12
        layer.masksToBounds = true
        layerBorderColor = .eColor
        layerBorderWidth = 0.5
        
        addSubview(giftSection)
        giftSection.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
        }
        
        // 设置礼物标题容器
        giftSection.addArrangedSubview(giftTitleContainer)
        
        giftTitleContainer.addSubview(giftTitleLabel)
        
        giftTitleLabel.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        
        giftTitleContainer.snp.makeConstraints { make in
            make.height.equalTo(20)
        }
        // 添加礼物CollectionView
        giftSection.addArrangedSubview(giftCollectionView)
        giftCollectionView.snp.makeConstraints { make in
            make.height.equalTo(56)
        }
        
    }

}


extension UserHonorVehicleView: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return datas.count
        
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: HonOrGiftItemCell.self, for: indexPath)
        var item = datas[indexPath.row]
        cell.giftImageView.setImage(from: item.goodsUrlShow)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 55, height: 55)
    }
    
}
