//
//  UserHonorCardView.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit

extension Array where Element == SkillLevelModel {
    /// 返回 *新的* 已降序排序的数组
    func sortedByTotalLevelDesc() -> [SkillLevelModel] {
        sorted {
            ($0.skillLevel + $0.skillAddLevel) >
            ($1.skillLevel + $1.skillAddLevel)
        }
    }
}


class UserHonorCardView: UIView {
    
    var model: UserInfoModel? {
        didSet {
            guard let model = model else { return }
            
            twoView.skillItems = model.skillLevel.sortedByTotalLevelDesc()
            giftView.setGiftItems(model.userGiftRecordList)
            giftView.setAtlasGiftItems(model.userAtlasGiftList)
            titleView.setTitleItems(model.userTitleWheat)
            twoView.scrollMessages = model.skillAttackedRecord
            let dd = model.userDecorateGoodsPropList.filter({ $0.goodsType == .mount })
            vehicView.datas = dd
            
            let one = model.famesValue.int.string
            
            let string = model.accountList.filter({ $0.accountType == 3 }).first?.accountBalance.int.string ?? "0"
            
            oneView.setAllHonorValues(prestige: one, charm: string, fans: model.fansCount.string)
            oneView.setRankingText(model.weeklyRankDescription)
        }
    }
    
    var stackView = UIStackView(arrangedSubviews: [], axis: .vertical, spacing: 15, alignment: .fill, distribution: .equalSpacing)
    
    lazy var oneView: UserHonorOneView = {
        let vv = UserHonorOneView()
        return vv
    }()
    
    lazy var twoView: UserHonorSkillView = {
        let skillView = UserHonorSkillView()
        return skillView
    }()
    
    lazy var titleView: UserHonorTitleView = {
        let vc = UserHonorTitleView()
        
        return vc
    }()
    
    lazy var giftView: UserHonorGiftView = {
        let vv = UserHonorGiftView()
        return vv
    }()
    
    lazy var vehicView: UserHonorVehicleView = {
        let vv = UserHonorVehicleView()
        return vv
    }()

    convenience init() {
        self.init(frame: .zero)
        
        addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview().inset(16)
        }
        
        stackView.addArrangedSubview(oneView)
        stackView.addArrangedSubview(twoView)
        stackView.addArrangedSubview(titleView)
        stackView.addArrangedSubview(giftView)
        stackView.addArrangedSubview(vehicView)
        
        
        titleView.onMoreTapped = { [weak self] in
            let vc = UserTitleMainVC()
            vc.userModel = self?.model
            AppTool.getCurrentViewController().navigationController?.pushViewController(vc, animated: true)
        }
        
        giftView.onGiftMoreTapped = { [weak self] in
            let vc = GiftWallMainVC()
            vc.imNum = self?.model?.imNumber.string
            vc.seletedIndex = 0
            AppTool.getCurrentViewController().navigationController?.pushViewController(vc)
        }
        
        giftView.onAtlasGiftMoreTapped = { [weak self] in
            let vc = GiftWallMainVC()
            vc.imNum = self?.model?.imNumber.string
            vc.seletedIndex = 1
            AppTool.getCurrentViewController().navigationController?.pushViewController(vc)
        }
    }
}
