//
//  UserHonorGiftView.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import SnapKit

class UserHonorGiftView: UIView {
    
    /// 普通礼物数据
    var giftItems: [GiftModel] = [] {
        didSet {
            updateGiftItems()
        }
    }
    
    /// 套系礼物数据
    var atlasGiftItems: [GiftListModel] = [] {
        didSet {
            updateAtlasGiftItems()
        }
    }
    
    /// 点击礼物更多回调
    var onGiftMoreTapped: (() -> Void)?
    
    /// 点击套系礼物更多回调
    var onAtlasGiftMoreTapped: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 主容器StackView
    private lazy var containerStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// 礼物区域容器
    private lazy var giftSection: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// 礼物标题容器
    private lazy var giftTitleContainer: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 礼物标题
    private lazy var giftTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "礼物"
        label.font = .medium(16)
        label.textColor = UIColor(hex: 0x333333)
        return label
    }()
    
    /// 礼物更多按钮
    private lazy var giftMoreButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_arrow_right"), for: .normal)
        button.addTarget(self, action: #selector(giftMoreButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 普通礼物CollectionView
    private lazy var giftCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(cellWithClass: HonOrGiftItemCell.self)
        return collectionView
    }()
    
    /// 套系礼物区域容器
    private lazy var atlasGiftSection: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// 套系礼物标题容器
    private lazy var atlasGiftTitleContainer: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 套系礼物标题
    private lazy var atlasGiftTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "套系礼物"
        label.font = .medium(16)
        label.textColor = UIColor(hex: 0x333333)
        return label
    }()
    
    /// 套系礼物更多按钮
    private lazy var atlasGiftMoreButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_arrow_right"), for: .normal)
        button.addTarget(self, action: #selector(atlasGiftMoreButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 套系礼物CollectionView
    private lazy var atlasGiftCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(AtlasGiftItemCell.self, forCellWithReuseIdentifier: "AtlasGiftItemCell")
        return collectionView
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = 12
        layer.masksToBounds = true
        layerBorderColor = .eColor
        layerBorderWidth = 0.5
        
        // 添加主容器
        addSubview(containerStackView)
        containerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        // 设置礼物区域
        setupGiftSection()
        
        // 设置套系礼物区域
        setupAtlasGiftSection()
        
        // 添加到主容器
        containerStackView.addArrangedSubview(giftSection)
        containerStackView.addArrangedSubview(atlasGiftSection)
    }
    
    private func setupGiftSection() {
        giftSection.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(goLiwu)))
        // 设置礼物标题容器
        giftSection.addArrangedSubview(giftTitleContainer)
        
        giftTitleContainer.addSubview(giftTitleLabel)
        giftTitleContainer.addSubview(giftMoreButton)
        
        giftTitleLabel.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        
        giftMoreButton.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        giftTitleContainer.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
        
        // 添加礼物CollectionView
        giftSection.addArrangedSubview(giftCollectionView)
        giftCollectionView.snp.makeConstraints { make in
            make.height.equalTo(60)
        }
    }
    
    @objc
    func goLiwu() {
        onGiftMoreTapped?()
    }
    
    @objc
    func goTaoXI() {
        onAtlasGiftMoreTapped?()
    }
    
    private func setupAtlasGiftSection() {
        atlasGiftSection.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(goTaoXI)))
        // 设置套系礼物标题容器
        atlasGiftSection.addArrangedSubview(atlasGiftTitleContainer)
        
        atlasGiftTitleContainer.addSubview(atlasGiftTitleLabel)
        atlasGiftTitleContainer.addSubview(atlasGiftMoreButton)
        
        atlasGiftTitleLabel.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        
        atlasGiftMoreButton.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        atlasGiftTitleContainer.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
        
        // 添加套系礼物CollectionView
        atlasGiftSection.addArrangedSubview(atlasGiftCollectionView)
        atlasGiftCollectionView.snp.makeConstraints { make in
            make.height.equalTo(110)
        }
    }
    

    
    private func updateGiftItems() {
        giftCollectionView.reloadData()
    }
    
    private func updateAtlasGiftItems() {
        atlasGiftCollectionView.reloadData()
        // 如果没有套系礼物，隐藏整个套系礼物区域
        atlasGiftSection.isHidden = atlasGiftItems.isEmpty
    }
    
    // MARK: - Actions
    
    @objc private func giftMoreButtonTapped() {
        onGiftMoreTapped?()
    }
    
    @objc private func atlasGiftMoreButtonTapped() {
        onAtlasGiftMoreTapped?()
    }
    
    // MARK: - Public Methods
    
    /// 设置普通礼物数据
    /// - Parameter gifts: 礼物数组
    func setGiftItems(_ gifts: [GiftModel]) {
        giftItems = gifts
    }
    
    /// 设置套系礼物数据
    /// - Parameter atlasGifts: 套系礼物数组
    func setAtlasGiftItems(_ atlasGifts: [GiftListModel]) {
        //过滤没有碎片的 但是集成到的
        var datas: [GiftListModel] = atlasGifts.filter({ $0.userNum > 0 })
        atlasGiftItems = datas
    }
    
    /// 设置套系礼物区域显示状态
    /// - Parameter isHidden: 是否隐藏
    func setAtlasGiftSectionHidden(_ isHidden: Bool) {
        atlasGiftSection.isHidden = isHidden
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate

extension UserHonorGiftView: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == giftCollectionView {
            return giftItems.count
        } else if collectionView == atlasGiftCollectionView {
            return atlasGiftItems.count
        }
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == giftCollectionView {
            let cell = collectionView.dequeueReusableCell(withClass: HonOrGiftItemCell.self, for: indexPath)
             cell.configure(with: giftItems[indexPath.item])
            return cell
        } else if collectionView == atlasGiftCollectionView {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "AtlasGiftItemCell", for: indexPath) as! AtlasGiftItemCell
             cell.configure(with: atlasGiftItems[indexPath.item])
            return cell
        }
        return UICollectionViewCell()
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if collectionView == giftCollectionView {
            return CGSize(width: 55, height: 55)
        } else if collectionView == atlasGiftCollectionView {
            return CGSize(width: 72, height: 108)
        }
        return CGSize.zero
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
      
    }
}

// MARK: - GiftItemCell (普通礼物Cell)

class HonOrGiftItemCell: UICollectionViewCell {
    
    /// 礼物图标
    lazy var giftImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layerCornerRadius = 0
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        
    }
    
    private func setupUI() {
        contentView.addSubview(giftImageView)
        giftImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func configure(with item: GiftModel) {
        // 配置礼物数据
        giftImageView.setImage(from: item.quietUrl)
    }
}
