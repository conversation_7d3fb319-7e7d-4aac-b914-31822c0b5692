//
//  UserHonorTitleView.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import SnapKit

class UserHonorTitleView: UIView {
    
    // MARK: - Properties
    
    /// 称号数据
    var titleItems: [UserTitleListModel] = [] {
        didSet {
            updateTitleItems()
        }
    }
    
    /// 点击更多回调
    var onMoreTapped: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 主容器StackView
    private lazy var containerStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// 标题容器
    private lazy var titleContainer: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "称号"
        label.font = .medium(16)
        label.textColor = UIColor(hex: 0x333333)
        return label
    }()
    
    /// 更多按钮
    private lazy var moreButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_arrow_right"), for: .normal)
        button.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 称号CollectionView
    private lazy var titleCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 8
        layout.minimumInteritemSpacing = 8
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(TitleItemCell.self, forCellWithReuseIdentifier: "TitleItemCell")
        return collectionView
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(onCilkAction)))
    }
    
    @objc
    func onCilkAction() {
        onMoreTapped?()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = 12
        layer.masksToBounds = true
        layerBorderColor = .eColor
        layerBorderWidth = 0.5
        
        // 添加主容器
        addSubview(containerStackView)
        containerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        // 设置标题容器
        setupTitleContainer()
        
        // 添加CollectionView
        containerStackView.addArrangedSubview(titleCollectionView)
        titleCollectionView.snp.makeConstraints { make in
            make.height.equalTo(32)
        }
    }
    
    private func setupTitleContainer() {
        containerStackView.addArrangedSubview(titleContainer)
        
        titleContainer.addSubview(titleLabel)
        titleContainer.addSubview(moreButton)
        
        titleLabel.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        
        moreButton.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        titleContainer.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
    }
    

    
    private func updateTitleItems() {
        titleCollectionView.reloadData()
        
    }
    
    // MARK: - Actions
    
    @objc private func moreButtonTapped() {
        onMoreTapped?()
    }
    
    // MARK: - Public Methods
    
    /// 设置称号数据
    /// - Parameter titles: 称号数组
    func setTitleItems(_ titles: [UserTitleListModel]) {
        titleItems = titles
    }
    
    /// 设置标题
    /// - Parameter title: 标题文本
    func setTitle(_ title: String) {
        titleLabel.text = title
    }
    
    /// 隐藏更多按钮
    /// - Parameter isHidden: 是否隐藏
    func setMoreButtonHidden(_ isHidden: Bool) {
        moreButton.isHidden = isHidden
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate

extension UserHonorTitleView: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return titleItems.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TitleItemCell", for: indexPath) as! TitleItemCell
        let item = titleItems[indexPath.item]
        cell.configure(with: item)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let item = titleItems[indexPath.item]
        return CGSize(width: 70, height: 24)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let item = titleItems[indexPath.item]
        
    }
}

// MARK: - TitleItemCell

class TitleItemCell: UICollectionViewCell {
    
    lazy var svgaV: SVGAnimationPlayer = {
        let player = SVGAnimationPlayer()
        return player
    }()

    
    lazy var titleImgV: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()
    
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    
    }
    
    
    private func setupUI() {
        contentView.addSubview(svgaV)
        svgaV.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.addSubview(titleImgV)
        titleImgV.snp.makeConstraints { make in
            make.edges.equalTo(svgaV)
        }
    }
    
    func configure(with item: UserTitleListModel) {
        svgaV.loadAnimation(named: item.codUrl ?? "")
        
        
    }
    
    func blackConfig(item: UserTitleListModel) {
        
        
        svgaV.snp.remakeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(66)
            make.height.equalTo(22)
        }
        
        
        contentView.layoutIfNeeded()
        
        if item.hasTitle {
            svgaV.loadAnimation(named: item.codUrl ?? "")
            svgaV.startAnimation()
            svgaV.isHidden = false
            titleImgV.isHidden = true
        } else {
            svgaV.stopAnimation()
            svgaV.isHidden = true
            titleImgV.isHidden = false
            titleImgV.blackImage(from: item.staticUrl)
            
        }
     
        contentView.backgroundColor = .white.withAlphaComponent(0.1)
        contentView.layerCornerRadius = 6
        
    }
 
} 
