//
//  AtlasGiftItemCell.swift
//  YINDONG
//
//  Created by jj on 2025/7/1.
//

import UIKit

class AtlasGiftItemCell: UICollectionViewCell {
    /// 卡片背景
    private lazy var cardBackgroundView: UIImageView = {
        let view = UIImageView()
        return view
    }()
    
    /// 套系图片
    private lazy var atlasImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 6
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    /// 套系名称
    private lazy var atlasNameLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(12)
        label.textColor = .textColor3
        label.textAlignment = .center
        return label
    }()
    
    /// 收集状态标签
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(8)
        label.textColor = UIColor(hex: 0x999999)
        label.textAlignment = .center
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        contentView.addSubview(cardBackgroundView)
        cardBackgroundView.addSubview(atlasImageView)
        cardBackgroundView.addSubview(atlasNameLabel)
        cardBackgroundView.addSubview(statusLabel)
        
        cardBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        atlasImageView.snp.makeConstraints { make in
            make.top.equalTo(9)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(64)
        }
        
        atlasNameLabel.snp.makeConstraints { make in
            make.top.equalTo(atlasImageView.snp.bottom).offset(2)
            make.left.right.equalToSuperview().inset(2)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(atlasNameLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(8)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
    
    func configure(with item: GiftListModel) {
        // 配置套系礼物数据
        atlasNameLabel.text = item.atlasName
        statusLabel.text = item.userNum == item.atlasNum ? "已集齐" : "\(item.userNum)/\(item.atlasNum)"
        statusLabel.textColor = UIColor(hexString: item.atlasBseDrawing ?? "")
        
        atlasImageView.setImage(from: item.atlasCover)
        cardBackgroundView.setImage(from: item.atlasBseDrawing)
    }
}
