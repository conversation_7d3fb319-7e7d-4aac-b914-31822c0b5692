//
//  UserHonorOneView.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import SnapKit

// MARK: - 荣誉卡片数据模型
struct HonorCardModel {
    let type: HonorType
    var value: String
    var isHidden: Bool = false
}

// MARK: - 荣誉类型
enum HonorType {
    case prestige  // 声望
    case charm     // 魅力  
    case fans      // 粉丝
    
    var title: String {
        switch self {
        case .prestige: return "声望"
        case .charm: return "魅力"
        case .fans: return "粉丝"
        }
    }
    
    var iconName: String {
        switch self {
        case .prestige: return "HonorOneV"
        case .charm: return "HonorMl" 
        case .fans: return "HonorFans"
        }
    }
    
    var textColor: UIColor? {
        switch self {
        case .prestige:
            return .init(hex: 0xD84631)
        case .charm:
            return .init(hex: 0xB237ED)
        case .fans:
            return .init(hex: 0x189DF0)
        }
    }
    
    var gradientColors: [UIColor?] {
        switch self {
        case .prestige: return [UIColor.init(hex: 0xFF6B47), UIColor.init(hex: 0xFF8A47)]
        case .charm: return [UIColor.init(hex: 0x9B59B6), UIColor.init(hex: 0xBB6BD9)]
        case .fans: return [UIColor.init(hex: 0x5DADE2), UIColor.init(hex: 0x85C1E9)]
        }
    }
}

class UserHonorOneView: UIView {
    
    // MARK: - Properties
    
    /// 荣誉卡片数据
    var honorCards: [HonorCardModel] = [] {
        didSet {
            updateHonorCards()
        }
    }
    
    /// 排行榜文本
    var rankingText: String = "" {
        didSet {
            updateRankingText()
        }
    }
    
    // MARK: - UI Components
    
    /// 主容器StackView - 垂直布局
    private lazy var containerStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()
    
    /// 荣誉卡片容器 - 横向布局
    private lazy var cardsContainerStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 12
        stackView.alignment = .fill
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    /// 排行榜信息容器
    private lazy var rankingContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.init(hex: 0xFFF3E0)
        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true
        return view
    }()
    
    /// 排行榜图标
    private lazy var rankingIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "icon_ranking") // 可以替换为合适的排行榜图标
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// 排行榜文本
    private lazy var rankingLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(12)
        label.textColor = UIColor.init(hex: 0xF57C00)
        label.numberOfLines = 0
        return label
    }()
    
    /// 荣誉卡片视图数组
    private var honorCardViews: [UIView] = []
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupDefaultData()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupDefaultData()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        layerBorderColor = .eColor
        layerBorderWidth = 0.5
        layer.cornerRadius = 12
        
        // 添加主容器
        addSubview(containerStackView)
        containerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        // 添加卡片容器
        containerStackView.addArrangedSubview(cardsContainerStackView)
        
        // 设置卡片容器高度
        cardsContainerStackView.snp.makeConstraints { make in
            make.height.equalTo(54)
        }
        
        // 添加排行榜容器
        containerStackView.addArrangedSubview(rankingContainer)
        setupRankingContainer()
    }
    
    private func setupRankingContainer() {
        // 添加排行榜图标和文本
        let horizontalStack = UIStackView()
        horizontalStack.axis = .horizontal
        horizontalStack.spacing = 8
        horizontalStack.alignment = .center
        horizontalStack.distribution = .fill
        
        rankingContainer.addSubview(horizontalStack)
        horizontalStack.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
        }
        
        // 添加图标
        horizontalStack.addArrangedSubview(rankingIconView)
        rankingIconView.snp.makeConstraints { make in
            make.width.height.equalTo(16)
        }
        
        // 添加文本
        horizontalStack.addArrangedSubview(rankingLabel)
        
        // 设置排行榜容器高度
        rankingContainer.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(20)
        }
    }
    
    // MARK: - Default Data
    
    private func setupDefaultData() {
        // 设置默认数据
        honorCards = [
            HonorCardModel(type: .prestige, value: "999999"),
            HonorCardModel(type: .charm, value: "9999999"), 
            HonorCardModel(type: .fans, value: "999")
        ]
        
        rankingText = "财富周榜第1，魅力周榜第2，战神周榜第49"
    }
    
    // MARK: - Data Updates
    
    private func updateHonorCards() {
        // 清除现有卡片
        honorCardViews.forEach { view in
            cardsContainerStackView.removeArrangedSubview(view)
            view.removeFromSuperview()
        }
        honorCardViews.removeAll()
        
        // 创建新卡片
        for card in honorCards {
            let cardView = createHonorCardView(with: card)
            cardsContainerStackView.addArrangedSubview(cardView)
            honorCardViews.append(cardView)
            
            // 设置显示/隐藏状态
            cardView.isHidden = card.isHidden
        }
    }
    
    private func createHonorCardView(with card: HonorCardModel) -> UIView {
        let containerView = UIView()
        containerView.layerCornerRadius = 8
        containerView.backgroundColor = .f8Color
        
        // 创建垂直StackView
        let verticalStack = UIStackView()
        verticalStack.axis = .vertical
        verticalStack.spacing = 0
        verticalStack.alignment = .center
        verticalStack.distribution = .fill
        
        // 数值标签
        let valueLabel = UILabel()
        valueLabel.text = card.value
        valueLabel.font = .medium(16)
        valueLabel.textColor = card.type.textColor
        valueLabel.textAlignment = .center
        valueLabel.adjustsFontSizeToFitWidth = true
        valueLabel.minimumScaleFactor = 0.5
        
        // 底部容器（图标+文字）
        let bottomStack = UIStackView()
        bottomStack.axis = .horizontal
        bottomStack.spacing = 4
        bottomStack.alignment = .center
        bottomStack.distribution = .fill
        
        // 荣誉图标
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(named: card.type.iconName)
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.snp.makeConstraints { make in
            make.width.height.equalTo(16)
        }
        
        // 标题标签
        let titleLabel = UILabel()
        titleLabel.text = card.type.title
        titleLabel.font = .regular(10)
        titleLabel.textColor = card.type.textColor
        titleLabel.textAlignment = .center
        
        // 组装底部
        bottomStack.addArrangedSubview(iconImageView)
        bottomStack.addArrangedSubview(titleLabel)
        
        // 组装垂直布局
        verticalStack.addArrangedSubview(valueLabel)
        verticalStack.addArrangedSubview(bottomStack)
        
        // 添加到容器
        containerView.addSubview(verticalStack)
        verticalStack.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(8)
        }
        
        return containerView
    }
    
    private func updateRankingText() {
        rankingLabel.text = rankingText
        rankingContainer.isHidden = rankingText.isEmpty
    }
    
    // MARK: - Public Methods
    
    /// 设置声望数值
    func setPrestigeValue(_ value: String) {
        updateHonorValue(for: .prestige, value: value)
    }
    
    /// 设置魅力数值
    func setCharmValue(_ value: String) {
        updateHonorValue(for: .charm, value: value)
    }
    
    /// 设置粉丝数值
    func setFansValue(_ value: String) {
        updateHonorValue(for: .fans, value: value)
    }
    
    /// 设置所有荣誉数值
    func setAllHonorValues(prestige: String, charm: String, fans: String) {
        var newCards = honorCards
        for i in 0..<newCards.count {
            switch newCards[i].type {
            case .prestige:
                newCards[i].value = prestige
            case .charm:
                newCards[i].value = charm
            case .fans:
                newCards[i].value = fans
            }
        }
        honorCards = newCards
    }
    
    /// 设置排行榜信息
    func setRankingText(_ text: String) {
        rankingText = text
    }
    
    /// 隐藏排行榜信息
    func setRankingHidden(_ isHidden: Bool) {
        UIView.animate(withDuration: 0.3) {
            self.rankingContainer.isHidden = isHidden
        }
    }
    
    /// 设置指定荣誉卡片的显示/隐藏状态
    func setHonorCardHidden(for type: HonorType, isHidden: Bool) {
        guard let index = honorCards.firstIndex(where: { $0.type == type }) else { return }
        honorCards[index].isHidden = isHidden
        
        if index < honorCardViews.count {
            UIView.animate(withDuration: 0.3) {
                self.honorCardViews[index].isHidden = isHidden
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func updateHonorValue(for type: HonorType, value: String) {
        guard let index = honorCards.firstIndex(where: { $0.type == type }) else { return }
        honorCards[index].value = value
        updateHonorCards()
    }
}
