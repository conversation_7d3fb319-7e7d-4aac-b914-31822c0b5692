//
//  UserHonorVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import JXPagingView

class UserHonorVC: YinDBaseVC, UITableViewDelegate {
    
    var listViewDidScrollCallback: ((UIScrollView) -> ())?

    lazy var carView: UserHonorCardView = {
        let cardV = UserHonorCardView()
        return cardV
    }()
    
    var model: UserInfoModel?

    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        loadData()
        
//        carView.refeshCallBack = { [weak self] allH in
//            guard let self = self else { return  }
//            self.infoView.height = CGFloat(allH)
//            self.tableView.tableHeaderView = self.infoView
//        }
        
        carView.height = 1100
        tableView.tableHeaderView = carView
        
    }
    
    override func loadData() {
        
        self.carView.model = model
        
    }

    override func lw_setupUI() {
        super.lw_setupUI()
        
        tableView.emptyDataSetSource = nil
        tableView.emptyDataSetDelegate = nil
        view.addSubview(tableView)
        tableView.delegate = self
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

}
extension UserHonorVC: JXPagingViewListViewDelegate {
    
    func listView() -> UIView {
        self.view
    }
    
    func listScrollView() -> UIScrollView {
        self.tableView
    }
    
    func listViewDidScrollCallback(callback: @escaping (UIScrollView) -> ()) {
        self.listViewDidScrollCallback  = callback
    }
    
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        self.listViewDidScrollCallback?(scrollView)
    }
}
