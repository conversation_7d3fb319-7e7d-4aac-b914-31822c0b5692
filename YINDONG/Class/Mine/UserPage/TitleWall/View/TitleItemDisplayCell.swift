//
//  TitleItemDisplayCell.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import SnapKit

class TitleItemDisplayCell: TitleItemCell {
    
    // MARK: - UI Components
    lazy var rankLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white.withAlphaComponent(0.6)
        label.font = .medium(12)
        label.textAlignment = .center
        return label
    }()
    
    /// 获得次数标签
    lazy var achievedLabel: UILabel = {
        let label = UILabel()
        label.text = "获得22次"
        label.textColor = .white
        label.font = .medium(12)
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupDisplayUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        
    }
    
    // MARK: - UI Setup
    
    private func setupDisplayUI() {

        
        svgaV.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(66)
            make.height.equalTo(22)
            make.top.equalToSuperview().inset(5)
        }
        
        // 添加排名标签
        contentView.addSubview(rankLabel)
        rankLabel.snp.makeConstraints { make in
            make.top.equalTo(svgaV.snp.bottom)
            make.centerX.equalToSuperview()
        }
        
        // 添加获得次数标签
        contentView.addSubview(achievedLabel)
        achievedLabel.snp.makeConstraints { make in
            make.top.equalTo(rankLabel.snp.bottom)
            make.centerX.equalToSuperview()
        }
    }
    // MARK: - Configuration
    
    /// 配置称号展示样式
    /// - Parameter item: 称号数据模型
    func configureDisplayStyle(with item: UserTitleListModel) {
        
        contentView.layoutIfNeeded()

        if item.hasTitle {
            svgaV.loadAnimation(named: item.codUrl ?? "")
            svgaV.startAnimation()
            svgaV.isHidden = false
            titleImgV.isHidden = true
        } else {
            svgaV.stopAnimation()
            svgaV.isHidden = true
            titleImgV.isHidden = false
            titleImgV.blackImage(from: item.staticUrl)
        }
        
        
        // 设置排名信息 (目前使用默认值，后续可根据具体数据调整)
        rankLabel.text = item.honorRemark
        // 设置获得次数 (目前使用默认值，后续可根据具体数据调整)
        
        if item.titleTypeName == "定制" || item.titleTypeName == "成就" {
            achievedLabel.text = item.name
        } else {
            achievedLabel.text = item.hasTitle ? "获得\(item.receiveNum)次" : "未点亮"
        }
        
        
    }
    
 
} 
