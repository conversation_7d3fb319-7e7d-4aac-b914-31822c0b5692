//
//  UserTitleHeaderView.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import SnapKit

class UserTitleHeaderView: UIView {
    // MARK: - Properties
    /// 用户数据
    var userModel: UserInfoModel? {
        didSet {
            updateUserInfo()
        }
    }
    
    /// 称号数据
    var titleItems: [UserTitleListModel] = [] {
        didSet {
            titleCollectionView.reloadData()
        }
    }
    
    /// 点击更多称号回调
    var onMoreTapped: (() -> Void)?
    
    // MARK: - UI Components
    
    /// 主背景容器
    private lazy var bgContainerView: UIImageView = {
        let view = UIImageView(image: UIImage(named: "user_titleBg"))
        return view
    }()
    
    /// 星星装饰左侧
    private lazy var leftStarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "left_t1")
        return imageView
    }()
    
    /// 星星装饰右侧
    private lazy var rightStarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "left_t2")
        
        return imageView
    }()
    
    /// 用户头像
    lazy var iconImgV: UIImageView = {
        let imgV = UIImageView()
        imgV.contentMode = .scaleAspectFill
        imgV.layer.cornerRadius = 30.5
        imgV.layerBorderColor = UIColor(hex: 0xFFA714)
        imgV.layer.borderWidth = 2
        imgV.clipsToBounds = true
        imgV.backgroundColor = UIColor.lightGray
        return imgV
    }()
    
    /// 用户昵称标题
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "我是昵称...的称号墙"
        label.textColor = .white
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textAlignment = .center
        return label
    }()
    
    /// 统计数据容器
    private lazy var statsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fill
        stackView.alignment = .center
        stackView.spacing = 20
        return stackView
    }()
    
    /// 拥有称号统计
    private lazy var titleCountView: UIView = {
        return createStatView(title: "拥有称号:", value: "26", color: UIColor(hex: 0xFFA714))
    }()
    
    /// 声望统计
    private lazy var reputationView: UIView = {
        return createStatView(title: "声望:", value: "5435", color: UIColor(hex: 0xFFA714))
    }()
    
    /// 排名统计
    private lazy var rankView: UIView = {
        return createStatView(title: "排名:", value: "99+>>", color: .white)
    }()
    
    /// 分隔线1 (称号和声望之间)
    private lazy var separatorLine1: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        view.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalTo(14)
        }
        return view
    }()
    
    /// 分隔线2 (声望和排名之间)
    private lazy var separatorLine2: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        view.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalTo(14)
        }
        return view
    }()
    
    /// 展示位标签
    private lazy var displayLabel: UILabel = {
        let label = UILabel()
        label.text = "展示位"
        label.textColor = .white.withAlphaComponent(0.87)
        label.font = .regular(12)
        return label
    }()
    
    /// 称号CollectionView
    private lazy var titleCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 40) // 右侧留空给箭头
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(cellWithClass: TitleItemCell.self)
        return collectionView
    }()
    
    /// 更多箭头按钮
    private lazy var moreButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "rightV_title"), for: .normal)
        button.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)
        return button
    }()

    convenience init() {
        self.init(frame: .zero)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // 添加主背景
        addSubview(bgContainerView)
        bgContainerView.snp.makeConstraints { make in
            make.top.equalTo(30) // 为头像留出空间
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview()
        }
        
        // 添加用户头像
        addSubview(iconImgV)
        iconImgV.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(61)
        }
        
        // 添加星星装饰
        bgContainerView.addSubview(leftStarImageView)
        bgContainerView.addSubview(rightStarImageView)
        
        
        // 添加标题
        bgContainerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(35)
            make.centerX.equalToSuperview()
            make.width.lessThanOrEqualTo(170)
        }
        
        leftStarImageView.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalTo(titleLabel.snp.left).offset(-8)
        }
        
        rightStarImageView.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.left.equalTo(titleLabel.snp.right).offset(8)
        }
        
        // 添加统计数据 - 固定布局，用隐藏控制
        statsStackView.addArrangedSubview(titleCountView)
        statsStackView.addArrangedSubview(separatorLine1)
        statsStackView.addArrangedSubview(reputationView)
        statsStackView.addArrangedSubview(separatorLine2)
        statsStackView.addArrangedSubview(rankView)
        
        bgContainerView.addSubview(statsStackView)
        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
        }
        
        // 初始化分隔线显示状态
        updateSeparatorVisibility()
        
        
        // 添加展示位标签
        bgContainerView.addSubview(displayLabel)
        displayLabel.snp.makeConstraints { make in
            make.bottom.equalTo(-15)
            make.left.equalTo(12)
        }
        
        // 添加称号CollectionView
        bgContainerView.addSubview(titleCollectionView)
        titleCollectionView.snp.makeConstraints { make in
            make.left.equalTo(56)
            make.right.equalTo(-50) // 为右侧箭头留出空间
            make.height.equalTo(21)
            make.centerY.equalTo(displayLabel)
        }
        
        // 添加更多按钮
        bgContainerView.addSubview(moreButton)
        moreButton.snp.makeConstraints { make in
            make.centerY.equalTo(titleCollectionView)
            make.right.equalTo(-20)
            make.width.height.equalTo(24)
        }
        
        // 设置整体高度约束
        self.snp.makeConstraints { make in
            make.height.equalTo(210) // 头像30 + 内容区180
        }
    }
    
    // MARK: - Helper Methods
    private func createStatView(title: String, value: String, color: UIColor?) -> UIView {
        let containerView = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = .textColor6
        titleLabel.font = .regular(14)
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.textColor = color
        valueLabel.font = .medium(14)
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(valueLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
        }
        
        valueLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.left.equalTo(titleLabel.snp.right).offset(5)
            make.right.equalToSuperview()
        }
        
        return containerView
    }
    
    
    private func updateUserInfo() {
        // 根据用户数据更新UI
        guard let userModel = userModel else { return }
        
        iconImgV.setImage(from: userModel.photoUrl)
        titleLabel.text = "\(userModel.nickName ?? "")的称号墙"
        titleItems = userModel.userTitleWheat
        
        // 只更新称号统计数量
        updateStatView(titleCountView, title: "拥有称号:", value: "\(userModel.userTitleCount)", color: UIColor(hex: 0xFFA714))
    }
    
    /// 更新统计视图的内容
    private func updateStatView(_ statView: UIView, title: String, value: String, color: UIColor?) {
        if let titleLabel = statView.subviews.first(where: { $0 is UILabel }) as? UILabel {
            titleLabel.text = title
        }
        if let valueLabel = statView.subviews.last(where: { $0 is UILabel }) as? UILabel {
            valueLabel.text = value
            valueLabel.textColor = color
        }
    }
    
    /// 更新分隔线的显示状态
    private func updateSeparatorVisibility() {
        let reputationVisible = !reputationView.isHidden
        let rankVisible = !rankView.isHidden
        
        // 分隔线1：如果声望显示，就显示分隔线1
        separatorLine1.isHidden = !reputationVisible
        
        // 分隔线2：如果声望和排名都显示，就显示分隔线2
        separatorLine2.isHidden = !(reputationVisible && rankVisible)
    }
    
    // MARK: - Public Methods
    
    /// 设置声望统计显示/隐藏
    func setReputationHidden(_ hidden: Bool) {
        reputationView.isHidden = hidden
        updateSeparatorVisibility()
    }
    
    /// 设置排名统计显示/隐藏  
    func setRankHidden(_ hidden: Bool) {
        rankView.isHidden = hidden
        updateSeparatorVisibility()
    }
    
    /// 更新声望数据
    func updateReputation(value: String) {
        updateStatView(reputationView, title: "声望:", value: value, color: UIColor(hex: 0xFFA714))
    }
    
    /// 更新排名数据
    func updateRank(value: String) {
        updateStatView(rankView, title: "排名:", value: value, color: .white)
    }
      
      // MARK: - Actions
    
    @objc private func moreButtonTapped() {
        onMoreTapped?()
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegate

extension UserTitleHeaderView: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return titleItems.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: TitleItemCell.self, for: indexPath)
        cell.configure(with: titleItems[indexPath.row])
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
    
        return CGSize(width: 70, height: 24) // 默认大小
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 处理称号点击事件
        collectionView.deselectItem(at: indexPath, animated: true)
    }
}


