//
//  UserRongYaoCell.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit

class UserRongYaoCell: UITableViewCell {
    
    var model: RyTitleModel? {
        didSet {
            guard let model = model else { return }
            
            
            
            BubbleImageLoader.shared.loadBubbleImage(urlString: model.coverUrl) { [weak self] resizable in
                guard let self = self else { return }
                if let resizable = resizable {
                    self.bgImgV.image = resizable
                }
            }

            
            collectionView.reloadData()
        }
    }
    
    var datas: [UserTitleListModel] {
        return model?.titleList ?? []
    }
    
    lazy var bgImgV: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()
    
    lazy var collectionView: UICollectionView = {
        let vv = UICollectionView(frame: .zero, collectionViewLayout: self.configLayout())
        vv.backgroundColor = .clear
        vv.showsVerticalScrollIndicator = false
        vv.showsHorizontalScrollIndicator = false
        return vv
    }()
    
    func configLayout() -> UICollectionViewFlowLayout {
       let layout = UICollectionViewFlowLayout()
       let cellW = (AppTool.screenWidth - 24) / 3
       layout.itemSize = CGSize(width: cellW, height: 65)
       layout.minimumInteritemSpacing = 0
       layout.minimumLineSpacing = 4
       layout.sectionInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
       return layout
   }


    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .clear
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        
        contentView.addSubview(bgImgV)
        bgImgV.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalTo(-15)
            make.top.equalToSuperview()
        }
        
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(cellWithClass: TitleItemDisplayCell.self)
        bgImgV.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(37)
            make.left.bottom.right.equalToSuperview()
        }
        
    }
    
}


extension UserRongYaoCell: UICollectionViewDataSource, UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: TitleItemDisplayCell.self, for: indexPath)
        cell.configureDisplayStyle(with: datas[indexPath.row])
        
      
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return datas.count
    }
    
}
