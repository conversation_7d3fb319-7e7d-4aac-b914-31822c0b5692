//
//  UserRongYaoListVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import JXSegmentedView

struct RyTitleModel: SmartCodable {
    var coverName: String?
    var coverUrl: String?
    var titleList = [UserTitleListModel]()
}
class UserRongYaoListVC: YinDBaseVC {
    
    var userId: String?
    
    var datas: [RyTitleModel] = []

    override func viewDidLoad() {
        super.viewDidLoad()

        loadData()
    }
    
    override func loadData() {
        // MARK: - 请求
        NetworkUtility.request(
            target: .userRyTitlts(["imNumber": userId ?? ""]),
            model: RyTitleModel.self,
            isList: true) { [weak self] result in
                
            guard let self = self, !result.isError else { return }
            self.datas = result.modelArr
            
            // ===== 在 “定制” 分组里插入占位 =====
            if let idx = self.datas.firstIndex(where: { $0.coverName == "定制" }) {
                var group = self.datas[idx]           // 取出 struct (值拷贝)
                
                let hasNobleCustom = group.titleList.contains { $0.type == 9 }
                let hasCPCustom   = group.titleList.contains { $0.type == 8 }
                
                // ---- 贵族定制称号占位 ----
                if !hasNobleCustom {
                    let noble = UserTitleListModel()
                    noble.id      = -1
                    noble.type    = 9                      // 对应安卓 type = 9
                    noble.titleTypeName = "定制"
                    noble.name = "贵族定制称号"    // 如需显示描述
                    noble.codUrl  = "https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/social/system/honorTitle/custom_noble.svga"
                    noble.staticUrl = "https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/social/system/honorTitle/custom_noble.png"
                    noble.hasTitle = false                 // 占位 → 尚未真正拥有
                    group.titleList.insert(noble, at: 0)   // 插到最前
                }
                
                // ---- CP 定制称号占位 ----
                if !hasCPCustom {
                    let cp = UserTitleListModel()
                    cp.id       = -1
                    cp.type     = 8                       // 安卓 type = 8
                    cp.titleTypeName = "定制"
                    cp.name = "CP定制称号"
                    cp.codUrl   = "https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/social/system/honorTitle/custom_cp.svga"
                    cp.staticUrl  = "https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/social/system/honorTitle/custom_cp.png"
                    cp.hasTitle  = false
                    group.titleList.append(cp)            // 放到尾部，保持安卓顺序
                }
                
                self.datas[idx] = group                   // 写回数组
            }
            
            self.tableView.reloadData()
        }
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        view.backgroundColor = .clear
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.register(cellWithClass: UserRongYaoCell.self)
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
    }

}

extension UserRongYaoListVC: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withClass: UserRongYaoCell.self)
        cell.model = datas[indexPath.row]
        return cell
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return datas.count
    }
    
    // datas[indexPath.row].titleList 就是你前面插好占位的数组
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        // 1️⃣ 取这一组的称号数量
        let count = datas[indexPath.row].titleList.count          // Int
        
        // 2️⃣ 每排 3 个，向上取整得到排数
        let rows = (count + 2) / 3       // 等价于 ceil(Double(count) / 3.0)
        
        // 3️⃣ 计算高度：基础 44 + rows × 65
        let height = 70 + rows * 65      // Int
        
        return CGFloat(height)
    }
    
}

extension UserRongYaoListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
