//
//  UserTitleMainVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import JXSegmentedView

class UserTitleMainVC: YinDBaseVC {
    
    var userModel: UserInfoModel?
    
    lazy var titleView: UserTitleHeaderView = {
        let vv = UserTitleHeaderView()
        return vv
    }()
    
    private var segmentedDataSource: JXSegmentedTitleDataSource = {
        var segmeDataSource = JXSegmentedTitleDataSource()
        segmeDataSource.titleNormalColor = .white.withAlphaComponent(0.6)
        segmeDataSource.titleSelectedColor = .white
        segmeDataSource.titleNormalFont = .regular(14)
        segmeDataSource.titleSelectedFont = .medium(16)
        segmeDataSource.isTitleColorGradientEnabled = true
        segmeDataSource.isItemSpacingAverageEnabled = false
        segmeDataSource.itemWidth = JXSegmentedViewAutomaticDimension
        segmeDataSource.titles = ["已拥有", "荣耀"]
        return segmeDataSource
    }()
    
    //初始化JXSegmentedView
    private lazy var segmentedView : JXSegmentedView = {
        let segmentedView = JXSegmentedView()
        segmentedView.delegate = self
        segmentedView.dataSource = self.segmentedDataSource
        segmentedView.listContainer = listContainerView
        let lineIndicator = JXSegmentedIndicatorLineView()
        lineIndicator.indicatorWidth = 16
        lineIndicator.indicatorHeight = 4
        lineIndicator.verticalOffset = 2
        lineIndicator.indicatorColor = .white
        lineIndicator.indicatorCornerRadius = 2
        segmentedView.indicators = [lineIndicator]
        
        return segmentedView
    }()
    
    // 列表视图高度封装的类
    private lazy var listContainerView: JXSegmentedListContainerView! = {
        let listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.listCellBackgroundColor = .clear
        return listContainerView
    }()


    override func viewDidLoad() {
        super.viewDidLoad()

        navigationBarAlpha = 0
        navigationBarTintColor = .white
     
        titleView.userModel = userModel

        if userModel?.imNumber.isIMMe == true {
            segmentedDataSource.titles = ["已拥有", "荣耀", "已拥有"]
            segmentedView.reloadData()
        }
        
        loadData()
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        view.backgroundColor = .clear
        
        
        let bgV = UIImageView(image: UIImage(named: "title_maxBg"))
        view.addSubview(bgV)
        bgV.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        view.addSubview(titleView)
        titleView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.navigationBarHeight)
            make.left.right.equalToSuperview()
            make.height.equalTo(185)
        }
        
        view.addSubview(segmentedView)
        view.addSubview(listContainerView)
        
        segmentedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(titleView.snp.bottom).offset(24)
            make.height.equalTo(40)
        }
        
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom).offset(10)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    override func loadData() {
        
        NetworkUtility.request(target: .userTitltRank(["imNumber": userModel?.imNumber ?? 0])) { result in
            if result.isError { return }
            
            
        }
        
    }

}
/// 列表视图数据源
extension UserTitleMainVC: JXSegmentedListContainerViewDataSource, JXSegmentedViewDelegate {
    
    // 返回列表的数量
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return segmentedDataSource.titles.count
    }
    
    // 返回遵从协议的实例
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        
        if index == 1 {
            let vc = UserRongYaoListVC()
            vc.userId = userModel?.imNumber.string
            return vc
        }
        
        let vc = UserTitleListVC()
        vc.userId = userModel?.imNumber.string
        vc.isNeed = index == 0
        return vc
    }
}
