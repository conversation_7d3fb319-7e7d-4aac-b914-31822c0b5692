//
//  UserTitleListVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/30.
//

import UIKit
import JXSegmentedView

class UserTitleListVC: YinDBaseVC {

    var userId: String?
    
    var isNeed: Bool = true
    
    var datas: [UserTitleListModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        loadData()
    }
    

    override func lw_setupUI() {
        super.lw_setupUI()
        
        view.backgroundColor = .clear
        
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(cellWithClass: TitleItemCell.self)
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    override func configLayout() -> UICollectionViewFlowLayout {
        let layout = UICollectionViewFlowLayout()
        let cellW = (AppTool.screenWidth - 73) / 3
        layout.itemSize = CGSize(width: cellW, height: 48)
        layout.minimumInteritemSpacing = 12
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
        return layout
    }
    
    override func loadData() {
        guard let userId = userId else { return }
        
        
        NetworkUtility.request(target: isNeed ? .userGetTitles(["imNumber": userId]) : .getTitleList, model: UserTitleListModel.self, isList: true) { result in
            if result.isError { return }
            
            self.datas = result.modelArr
            self.collectionView.reloadData()
        }
        
    }

}

extension UserTitleListVC: UICollectionViewDataSource, UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: TitleItemCell.self, for: indexPath)
        cell.blackConfig(item: datas[indexPath.row])
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return datas.count
    }
    
}

extension UserTitleListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
