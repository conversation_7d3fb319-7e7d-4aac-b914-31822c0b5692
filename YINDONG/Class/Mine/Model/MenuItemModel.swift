//
//  MenuItemModel.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit

// MARK: - 菜单分类
enum MenuCategory {
    case function  // 功能区域
    case service   // 服务区域
}

// MARK: - 菜单项类型枚举
enum MenuItemType: String, CaseIterable {
    // 功能区域菜单
    case family = "family"           // 家族
    case cpRoom = "cp_room"          // CP小屋
    case backpack = "backpack"       // 背包
    case title = "title"             // 称号
    case workshop = "workshop"       // 工馆
    case checkin = "checkin"         // 签到
    case newbieGift = "newbie_gift"  // 新人礼包
    case skill = "skill"             // 技能
    case store = "store"             // 商店
    
    // 服务区域菜单
    case share = "share"             // 分享推荐
    case help = "help"               // 帮助与反馈
    case settings = "settings"       // 设置
    
    var displayName: String {
        switch self {
        // 功能区域
        case .family: return "家族"
        case .cpRoom: return "CP小屋"
        case .backpack: return "背包"
        case .title: return "称号"
        case .workshop: return "工馆"
        case .checkin: return "签到"
        case .newbieGift: return "新人礼包"
        case .skill: return "技能"
        case .store: return "商店"
        
        case .share: return "分享推荐"
        case .help: return "帮助与反馈"
        case .settings: return "设置"
        }
    }
    
    var iconName: String {
        switch self {
        // 功能区域
        case .family: return "icon_family"
        case .cpRoom: return "icon_cp"
        case .backpack: return "icon_me_bk"
        case .title: return "icon_titleWall"
        case .workshop: return "icon_me_gg"
        case .checkin: return "icon_checkin"
        case .newbieGift: return "icon_newbie"
        case .skill: return "icon_skill"
        case .store: return "icon_store"
        
        // 服务区域
        case .share: return "icon_share_me"
        case .help: return "icon_help_me"
        case .settings: return "icon_setting_me"
        }
    }
    
    var isEnabled: Bool {
        switch self {
        case .family, .backpack, .skill, .store, .share, .help, .settings:
            return true
        case .cpRoom, .title, .workshop, .checkin, .newbieGift:
            return !appCodelw
        
        }
    }
    
    /// 菜单项所属区域
    var category: MenuCategory {
        switch self {
        case .family, .cpRoom, .backpack, .title, .workshop, .checkin, .newbieGift, .skill, .store:
            return .function
        case .share, .help, .settings:
            return .service
        }
    }
}

// MARK: - 菜单项数据模型
struct MenuItemModel {
    let type: MenuItemType
    let badgeCount: Int?        // 角标数量
    let isNew: Bool             // 是否显示新标识
    let isEnabled: Bool         // 是否可点击
    
    init(type: MenuItemType, badgeCount: Int? = nil, isNew: Bool = false, isEnabled: Bool? = nil) {
        self.type = type
        self.badgeCount = badgeCount
        self.isNew = isNew
        self.isEnabled = isEnabled ?? type.isEnabled
    }
    
    var displayName: String { type.displayName }
    var iconName: String { type.iconName }
    var category: MenuCategory { type.category }
}
