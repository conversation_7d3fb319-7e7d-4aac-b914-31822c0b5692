//
//  NobilityListVC.swift
//  YINDONG
//
//  Created by jj on 2025/7/2.
//

import UIKit
import JXSegmentedView
import QGVAPlayer

class NobilityListVC: YinDBaseVC, HWDMP4PlayDelegate {

    let animView = UIView()
    
    var index: Int = 0
    
    var model: NobilityConfigModel?
    
    var levelModel: LevelsConfig? {
        return model?.levels[index]
    }
    
    var allDatas: [FunctionsConfig] {
        let allData = model?.functions ?? []
        allData.forEach { config in
            config.isLight = config.levelId <= (levelModel?.id ?? 0)
        }
        showCount = allData.filter({ $0.isLight }).count
        return allData
    }
    
    lazy var contentView: UIView = {
        let vv = UIView()
        vv.layerBorderColor = .init(hex: 0xDAC6B2)
        vv.layerBorderWidth = 1
        vv.layerCornerRadius = 20
        vv.backgroundColor = .init(hex: 0x212123)
        return vv
    }()
    
    
    lazy var showTitle: UILabel = {
        let labe = UILabel(withText: "专属特权", textColor: .init(hex: 0x322320), fontType: .medium(16))
        return labe
    }()
    
 
    
    var showCount: Int = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()
        

        // Do any additional setup after loading the view.
        guard let levelModel = levelModel else { return }
      
        if levelModel.url?.contains("noble_6") == true || levelModel.url?.contains("noble_8") == true {
            animView.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
            
            if levelModel.url?.contains("noble_6") == true {
                animView.snp.updateConstraints { make in
                    make.width.equalTo(240)
                    make.height.equalTo(200)
                }
            }
            
        }
        
        FileManagerWrapper.shared.downloadVap(url: levelModel.url ?? "") { [weak self] path in
            guard let self = self else { return }
            Async.main(after: 0.01) {
                self.animView.playHWDMP4(path, repeatCount: Int.max, delegate: self)
            }
            TLog("本地文件-----\(path)")
        } failure: { error in
            
        }
        
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        view.backgroundColor = .clear
        
        
        let imgV = UIImageView(image: UIImage(named: "noble_page_bg_2"))
        view.addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(30)
        }
        
        let imgV1 = UIImageView(image: UIImage(named: "noble_page_bg_3"))
        view.addSubview(imgV1)
        imgV1.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(200)
        }
        
        view.addSubview(animView)
        animView.snp.makeConstraints { make in
            make.centerX.top.equalToSuperview()
            make.width.height.equalTo(225)
        }
        
        view.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(8)
            make.top.equalTo(animView.snp.bottom).offset(70)
            make.height.equalTo(200)
        }
        
        
        let imgV111 = UIImageView(image: UIImage(named: "noble_num_bg"))
        view.addSubview(imgV111)
        imgV111.snp.makeConstraints { make in
            make.centerX.equalTo(contentView)
            make.top.equalTo(contentView).offset(-20)
        }
        
        view.addSubview(showTitle)
        showTitle.snp.makeConstraints { make in
            make.center.equalTo(imgV111)
        }
        
        
        collectionView.register(cellWithClass: FunctionItemCell.self)
        collectionView.dataSource = self
        collectionView.delegate = self
        contentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(35)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(-15)
        }
        
    }


    override func configLayout() -> UICollectionViewFlowLayout {
        let layout = UICollectionViewFlowLayout()
        let wi = (AppTool.screenWidth - 17) / 3
        layout.itemSize = CGSizeMake(wi, 90)
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        return layout
    }

}


extension NobilityListVC: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        showTitle.text = "专属特权 \(showCount)/\(allDatas.count)"
        return allDatas.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "FunctionItemCell", for: indexPath) as! FunctionItemCell
        let item = allDatas[indexPath.item]
        cell.configureNob(item: item)
        return cell
    }
}


extension NobilityListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
