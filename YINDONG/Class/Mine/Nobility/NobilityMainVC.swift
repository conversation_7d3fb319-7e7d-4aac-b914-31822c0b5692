//
//  NobilityMainVC.swift
//  YINDONG
//
//  Created by jj on 2025/7/2.
//

import UIKit
import JXSegmentedView

class NobilityMainVC: YinDBaseVC {
    
    var dataModel: NobilityConfigModel?
    
    lazy var titleVV: InfoItemBaseView = {
        let infoV = InfoItemBaseView(rightImg: "", leftTitle: "荣耀贵族", space: 2, imgWH: CGSizeMake(13, 13), margin: 0)
        infoV.imgV.image = UIImage(named: "room_wenti")?.tint(with: .init(hex: 0xEDD9C9))
        infoV.titleLab.font = .medium(16)
        infoV.titleLab.textColor = .init(hex: 0xEDD9C9)
        return infoV
    }()
    
    
    private var segmentedDataSource: JXSegmentedTitleDataSource = {
        var segmeDataSource = JXSegmentedTitleDataSource()
        segmeDataSource.titleNormalColor = .init(hex: 0x7C7C7E) ?? .white.withAlphaComponent(0.6)
        segmeDataSource.titleSelectedColor = .init(hex: 0xEDD9C9) ?? .white
        segmeDataSource.titleNormalFont = .regular(14)
        segmeDataSource.titleSelectedFont = .regular(20)
        segmeDataSource.isTitleColorGradientEnabled = true
        segmeDataSource.isTitleZoomEnabled = true
        segmeDataSource.titleSelectedZoomScale = 1.5
        segmeDataSource.isItemSpacingAverageEnabled = false
        segmeDataSource.itemWidth = 60
//        segmeDataSource.itemSpacing = 20
        return segmeDataSource
    }()
    
    //初始化JXSegmentedView
    private lazy var segmentedView : JXSegmentedView = {
        let segmentedView = JXSegmentedView()
        segmentedView.delegate = self
        segmentedView.dataSource = self.segmentedDataSource
        segmentedView.listContainer = listContainerView
        let lineIndicator = JXSegmentedIndicatorLineView()
        lineIndicator.indicatorWidth = 16
        lineIndicator.indicatorHeight = 4
        lineIndicator.verticalOffset = 4
        lineIndicator.indicatorColor = .init(hex: 0xEDD9C9) ?? .white
        lineIndicator.indicatorCornerRadius = 2
        segmentedView.indicators = [lineIndicator]
        
        return segmentedView
    }()
    
    // 列表视图高度封装的类
    private lazy var listContainerView: JXSegmentedListContainerView! = {
        let listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.listCellBackgroundColor = .clear
        return listContainerView
    }()

    lazy var rightBtn: UIButton = {
        let btn = UIButton(frame: CGRectMake(0, 0, 70, 20))
        btn.tx_Img("single")
        return btn
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        navigationBarAlpha = 0
        
        navigation.item.titleView = titleVV
        
        navigation.item.rightBarButtonItem = UIBarButtonItem(customView: rightBtn)
        
        navigationTitleColor = .init(hex: 0xEDD9C9)
        navigationBarTintColor = .init(hex: 0xEDD9C9)
        loadData()
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        view.backgroundColor = .init(hex: 0x0E0E0E)
        
        let imgV = UIImageView(image: UIImage(named: "noble_page_bg_1"))
        view.addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
        }
        
        
        
        view.addSubview(segmentedView)
        view.addSubview(listContainerView)
        
        segmentedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(AppTool.navigationBarHeight)
            make.height.equalTo(44)
        }
        
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom).offset(0)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(46 + AppTool.safeAreaBottomHeight)
        }
    }
    
    
    override func loadData() {
        
        NetworkUtility.request(target: .nobility_config) { result in
            self.dataModel = NobilityConfigModel.deserialize(from: result.json?.dictionaryObject)
            self.reloadInfo()
        }
        
        
        
    }
    
    func reloadInfo() {
        guard let dataModel = dataModel else { return }
        
        segmentedDataSource.titles = dataModel.levels.compactMap({ return $0.name })
        segmentedView.reloadData()
        
    }
    
    


}
/// 列表视图数据源
extension NobilityMainVC: JXSegmentedListContainerViewDataSource, JXSegmentedViewDelegate {
    
    // 返回列表的数量
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return segmentedDataSource.titles.count
    }
    
    // 返回遵从协议的实例
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        
        let vc = NobilityListVC()
        vc.index = index
        vc.model = dataModel
        return vc
    }
}
