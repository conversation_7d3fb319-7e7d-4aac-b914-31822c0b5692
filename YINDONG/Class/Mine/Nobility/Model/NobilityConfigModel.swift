//
//  NobilityConfigModel.swift
//  YINDONG
//
//  Created by jj on 2025/7/2.
//

import UIKit

class NobilityConfigModel: SmartCodable {
    var functions = [FunctionsConfig]()
    var levels = [LevelsConfig]()
    var valueProblem: String?
    
    required init() {
        
    }
}
class LevelsConfig: SmartCodable {
    var id: Int = 0
    var name: String?
    var remarks: String?
    var url: String?
    var value: Int = 0
    required init() {
        
    }
}

class FunctionsConfig: SmartCodable {
    @SmartAny
    var detail: [String: String]?
    var detailUrl: String?
    var isCenter: Bool = false
    var levelId: Int = 0
    var levelName: String?
    var name: String?
    var remarks: String?
    var url: String?
    
    var isLight: Bool = false
    
    required init() {
        
    }
}
